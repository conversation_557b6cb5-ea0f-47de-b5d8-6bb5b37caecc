#define _GNU_SOURCE
#include "auth_server.h"
#include "background_task.h"
#include "config_loader.h"
#include "daemonize.h"
#include "device_manager.h"
#include "log.h"
#include "oake.h"
#include "oake_logic.h"
#include "packet_utils.h"
#include "raw_sender.h"
#include "send_utils.h"
#include <arpa/inet.h>
#include <errno.h>
#include <libnetfilter_queue/libnetfilter_queue.h>
#include <linux/netfilter.h> /* for NF_ACCEPT */
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>

// Global variables for signal handler cleanup
static struct nfq_handle *g_nfq_handle = NULL;
static struct nfq_q_handle *g_nfq_q_handle = NULL;
static volatile sig_atomic_t g_shutdown_flag = 0;

// Forward declaration for cleanup function
static void cleanup_resources(struct nfq_handle *h, struct nfq_q_handle *qh);

// External cleanup function that can be called from daemonize.c
void daemon_cleanup(void) {
  log_info("Starting daemon cleanup process");

  // Clean up global handles if available
  cleanup_resources(g_nfq_handle, g_nfq_q_handle);

  log_info("Daemon cleanup process completed");
}

// Signal handler for graceful shutdown
static void signal_handler(int sig) {
  log_warn("Received signal %d, initiating graceful shutdown", sig);
  g_shutdown_flag = 1;

  // Perform immediate cleanup
  cleanup_resources(g_nfq_handle, g_nfq_q_handle);

  // Exit the program
  exit(0);
}

// Function to setup signal handlers
static void setup_signal_handlers(void) {
  struct sigaction sa;
  sa.sa_handler = signal_handler;
  sigemptyset(&sa.sa_mask);
  sa.sa_flags = 0;

  // Handle SIGINT (Ctrl-C)
  if (sigaction(SIGINT, &sa, NULL) == -1) {
    log_error("Failed to set SIGINT handler: %s", strerror(errno));
  }

  // Handle SIGTERM (termination signal)
  if (sigaction(SIGTERM, &sa, NULL) == -1) {
    log_error("Failed to set SIGTERM handler: %s", strerror(errno));
  }

  // Handle SIGQUIT (quit signal)
  if (sigaction(SIGQUIT, &sa, NULL) == -1) {
    log_error("Failed to set SIGQUIT handler: %s", strerror(errno));
  }
}

static int packet_handler(struct nfq_q_handle *qh, struct nfgenmsg *nfmsg,
                          struct nfq_data *nfa, void *data) {
  (void)nfmsg;
  (void)data;
  u_int32_t id = 0;
  struct nfqnl_msg_packet_hdr *ph;
  unsigned char *payload_data;
  int payload_len;

  ph = nfq_get_msg_packet_hdr(nfa);
  if (ph) {
    id = ntohl(ph->packet_id);
  }

  payload_len = nfq_get_payload(nfa, &payload_data);
  print_packet_info(id, payload_data, payload_len);

  if (process_and_send_packet(payload_data, payload_len) < 0) {
    log_error("Error sending packet via process_and_send_packet");
  }

  // Packet has been handled by process_and_send_packet, so drop the original
  // to prevent it from being sent again by the kernel.
  return nfq_set_verdict(qh, id, NF_DROP, 0, NULL);
}
static void initialize_logging(const task_config_t *config) {
  if (config->logging.log_to_file) {
    FILE *log_fp = fopen(config->logging.log_filename, "a");
    if (log_fp) {
      log_add_fp(log_fp, config->logging.log_level);
      log_info("Logging initialized to file: %s", config->logging.log_filename);
    } else {
      log_error("Failed to open log file: %s", config->logging.log_filename);
    }
  }
}

static const task_config_t *initialize_configuration() {
  initialize_global_config("config.ini");
  const task_config_t *config = get_global_config();
  if (!config) {
    log_fatal("Failed to load configuration. Exiting");
    exit(1);
  }
  return config;
}

static int start_services(const task_config_t *config) {
  if (start_auth_server(config) != 0) {
    log_fatal("Failed to start authentication server. Exiting");
    return -1;
  }

  if (start_oake_thread(config) != 0) {
    log_fatal("Failed to start OAKE thread. Exiting");
    return -1;
  }

  init_background_task();

  return 0;
}

static int initialize_packet_systems(const task_config_t *config) {
  if (init_sitp_sender(config->sitp_task.sitp_q_cap) != 0) {
    log_fatal("Failed to initialize SITP sender system. Exiting");
    return -1;
  }
  return 0;
}

static int setup_netfilter_queue(const task_config_t *config,
                                 struct nfq_handle **h_ptr,
                                 struct nfq_q_handle **qh_ptr) {
  log_info("Opening netfilter queue library handle");
  *h_ptr = nfq_open();
  if (!*h_ptr) {
    log_error("Error during nfq_open()");
    return -1;
  }

  log_debug("Unbinding existing nf_queue handler for AF_INET (if any)");
  if (nfq_unbind_pf(*h_ptr, AF_INET) < 0) {
    log_warn("Warning: Error during nfq_unbind_pf(). Continuing...");
    // We can often ignore this error if no previous handler was bound
  }

  log_info("Binding nfnetlink_queue as nf_queue handler for AF_INET");
  if (nfq_bind_pf(*h_ptr, AF_INET) < 0) {
    log_error("Error during nfq_bind_pf()");
    nfq_close(*h_ptr);
    *h_ptr = NULL;
    return -1;
  }

  log_info("Binding this socket to queue %u", config->sitp_task.queue_num);
  *qh_ptr = nfq_create_queue(*h_ptr, config->sitp_task.queue_num,
                             &packet_handler, NULL);
  if (!*qh_ptr) {
    log_error("Error during nfq_create_queue()");
    nfq_unbind_pf(*h_ptr, AF_INET);
    nfq_close(*h_ptr);
    *h_ptr = NULL;
    return -1;
  }

  log_debug("Setting copy_packet mode");
  if (nfq_set_mode(*qh_ptr, NFQNL_COPY_PACKET, 0xffff) < 0) {
    log_error("Can't set packet_copy mode");
    nfq_destroy_queue(*qh_ptr);
    *qh_ptr = NULL;
    nfq_unbind_pf(*h_ptr, AF_INET);
    nfq_close(*h_ptr);
    *h_ptr = NULL;
    return -1;
  }
  return 0;
}

static void run_packet_loop(struct nfq_handle *h, int fd) {
  char buf[IP_MAXPACKET] __attribute__((aligned));
  int rv;

  log_info("Waiting for packets...");
  while (!g_shutdown_flag && (rv = recv(fd, buf, sizeof(buf), 0)) && rv >= 0) {
    nfq_handle_packet(h, buf, rv);
  }
  if (rv < 0) {
    log_error("recv failed: %s", strerror(errno));
  }
}

static void cleanup_resources(struct nfq_handle *h, struct nfq_q_handle *qh) {
  log_info("Starting cleanup process");

  // Clear OAKE data first
  log_debug("Clearing OAKE data");
  oake_clear_all_data();
  log_debug("OAKE data cleared");

  if (qh) {
    log_debug("Unbinding from queue");
    nfq_destroy_queue(qh);
  }

#ifdef INSANE
  if (h) {
    /* normally, applications SHOULD NOT issue this command, since
     * it detaches other programs/sockets from AF_INET, too ! */
    log_debug("Unbinding from AF_INET");
    nfq_unbind_pf(h, AF_INET);
  }
#endif

  if (h) {
    log_debug("Closing library handle");
    nfq_close(h);
  }

  log_info("Cleaning up SITP sender system");
  cleanup_sitp_sender();
  log_debug("SITP sender system cleaned up");

  cleanup_raw_sender();
  log_debug("Raw sender system cleaned up");

  log_info("Cleaning up device manager");
  device_cleanup();
  log_debug("Device manager cleaned up");

  log_info("Cleanup process completed");
}

int daemon_start() {
  const task_config_t *config = initialize_configuration();
  initialize_logging(config);

  if (device_init() != 0) {
    log_fatal("Failed to initialize device. Exiting");
  }

  if (device_load_global_certs() != 0) {
    log_fatal("Failed to load device certificates. Exiting");
    device_cleanup();
  }

  if (start_services(config) != 0) {
    device_cleanup();
    cleanup_raw_sender();
    exit(1);
  }

  if (initialize_packet_systems(config) != 0) {
    device_cleanup();
    cleanup_raw_sender();
    exit(1);
  }

  struct nfq_handle *h = NULL;
  struct nfq_q_handle *qh = NULL;
  int fd;

  if (setup_netfilter_queue(config, &h, &qh) != 0) {
    cleanup_sitp_sender();
    device_cleanup();
    cleanup_raw_sender();
    exit(1);
  }

  // Store handles in global variables for signal handler access
  g_nfq_handle = h;
  g_nfq_q_handle = qh;

  // Check if we're running as a daemon (stdout redirected to /dev/null)
  // If not, setup our own signal handlers
  struct stat stat_buf;
  if (fstat(STDOUT_FILENO, &stat_buf) == 0) {
    // Check if stdout is /dev/null (typical for daemon mode)
    struct stat null_stat;
    if (stat("/dev/null", &null_stat) == 0 &&
        stat_buf.st_dev == null_stat.st_dev &&
        stat_buf.st_ino == null_stat.st_ino) {
      log_info("Running in daemon mode, using daemonize.c signal handlers");
    } else {
      // Setup signal handlers for graceful shutdown (non-daemon mode)
      log_info("Setting up signal handlers for non-daemon mode");
      setup_signal_handlers();
    }
  } else {
    // If we can't determine, setup signal handlers anyway
    log_info("Setting up signal handlers");
    setup_signal_handlers();
  }

  fd = nfq_fd(h);
  run_packet_loop(h, fd);

  cleanup_resources(h, qh);

  exit(0);
}

int main(int argc, const char **argv) { return daemon_main(argc, argv); }
