#include "config_loader.h"
#include "include/sm_algo.h" // For algorithm and mode integer definitions
#include "ini.h"
#include "log.h"
#include <ctype.h> // For isspace
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h> // For strcasecmp

// Helper macros for parsing values
#define PARSE_STRING(dest, src, max_len)                                       \
  do {                                                                         \
    strncpy((dest), (src), (max_len) - 1);                                     \
    (dest)[(max_len) - 1] = '\0';                                              \
  } while (0)

#define PARSE_INT(dest, src)                                                   \
  do {                                                                         \
    (dest) = atoi(src);                                                        \
  } while (0)

#define PARSE_U16(dest, src)                                                   \
  do {                                                                         \
    (dest) = (uint16_t)strtol((src), NULL, 0);                                 \
  } while (0)

#define PARSE_UINT(dest, src)                                                  \
  do {                                                                         \
    (dest) = (unsigned int)strtoul((src), NULL, 0);                            \
  } while (0)

#define PARSE_BOOL(dest, src)                                                  \
  do {                                                                         \
    if (strcasecmp(src, "true") == 0 || strcmp(src, "1") == 0) {               \
      (dest) = 1;                                                              \
    } else {                                                                   \
      (dest) = 0;                                                              \
    }                                                                          \
  } while (0)

// Global configuration instance
static task_config_t g_task_config_instance;
static int g_config_initialized = 0; // Flag to check if initialized

// Forward declarations for static functions
static void set_default_config(task_config_t *config);
static void print_task_config(const task_config_t *config,
                              const char *filename);
static int parse_sitp_task_config(sitp_task_config_t *config, const char *name,
                                  const char *value);
static int parse_csl_config(csl_config_t *config, const char *name,
                            const char *value);
static int parse_auth_server_config(auth_server_config_t *config,
                                    const char *name, const char *value);
static int parse_logging_config(logging_config_t *config, const char *name,
                                const char *value);
static char *trim_whitespace(char *str);
static int handler(void *user, const char *section, const char *name,
                   const char *value);

// Helper function to convert log level string to int
static int parse_log_level(const char *str) {
  if (strcasecmp(str, "TRACE") == 0)
    return LOG_TRACE;
  if (strcasecmp(str, "DEBUG") == 0)
    return LOG_DEBUG;
  if (strcasecmp(str, "INFO") == 0)
    return LOG_INFO;
  if (strcasecmp(str, "WARN") == 0)
    return LOG_WARN;
  if (strcasecmp(str, "ERROR") == 0)
    return LOG_ERROR;
  if (strcasecmp(str, "FATAL") == 0)
    return LOG_FATAL;
  return LOG_INFO; // Default to INFO for unknown values
}

// Helper function to trim leading/trailing whitespace
static char *trim_whitespace(char *str) {
  char *end;

  // Trim leading space
  while (isspace((unsigned char)*str))
    str++;

  if (*str == 0) // All spaces?
    return str;

  // Trim trailing space
  end = str + strlen(str) - 1;
  while (end > str && isspace((unsigned char)*end))
    end--;

  // Write new null terminator
  *(end + 1) = 0;

  return str;
}

static int parse_sitp_task_config(sitp_task_config_t *config, const char *name,
                                  const char *value) {
  if (strcmp(name, "eth_dev") == 0) {
    PARSE_STRING(config->eth_dev, value, MAX_ETH_DEV_LEN);
  } else if (strcmp(name, "mtu") == 0) {
    PARSE_INT(config->mtu, value);
  } else if (strcmp(name, "protocol") == 0) {
    PARSE_U16(config->protocol, value);
  } else if (strcmp(name, "local_id") == 0) {
    PARSE_U16(config->local_id, value);
  } else if (strcmp(name, "remote_id") == 0) {
    PARSE_U16(config->remote_id, value);
  } else if (strcmp(name, "local_port") == 0) {
    PARSE_U16(config->local_port, value);
  } else if (strcmp(name, "remote_port") == 0) {
    PARSE_U16(config->remote_port, value);
  } else if (strcmp(name, "pkt_trim_bytes") == 0) {
    PARSE_UINT(config->pkt_trim_bytes, value);
  } else if (strcmp(name, "sitp_q_cap") == 0) {
    PARSE_INT(config->sitp_q_cap, value);
  } else if (strcmp(name, "send_delay") == 0) {
    PARSE_UINT(config->send_delay, value);
  } else if (strcmp(name, "queue_num") == 0) {
    PARSE_U16(config->queue_num, value);
  } else {
    return 0; // Unknown name
  }
  return 1; // Success
}

static int parse_csl_config(csl_config_t *config, const char *name,
                            const char *value) {
  if (strcmp(name, "device_no") == 0) {
    PARSE_INT(config->device_no, value);
  } else if (strcmp(name, "pin") == 0) {
    PARSE_STRING(config->pin, value, MAX_PIN_LEN);
  } else if (strcmp(name, "enc_cert_name") == 0) {
    PARSE_STRING(config->enc_cert_name, value, ENC_CERT_NAME_LEN);
  } else if (strcmp(name, "sign_cert_name") == 0) {
    PARSE_STRING(config->sign_cert_name, value, SIGN_CERT_NAME_LEN);
  } else if (strcmp(name, "iv") == 0) {
    PARSE_STRING(config->iv, value, IV_LEN);
  } else if (strcmp(name, "encrypt") == 0) {
    PARSE_INT(config->encrypt, value);
  } else if (strcmp(name, "symm_algo") == 0) {
    if (strcmp(value, "PFSJ1006B") == 0) {
      config->symm_algo = PFSJ1006B;
    } else if (strcmp(value, "DPZ1801") == 0) {
      config->symm_algo = DPZ1801;
    } else {
      PARSE_UINT(config->symm_algo, value);
    }
  } else if (strcmp(name, "asymm_algo") == 0) {
    if (strcmp(value, "P1GWY0613_IEA") == 0) {
      config->asymm_algo = P1GWY0613_IEA;
    } else {
      PARSE_UINT(config->asymm_algo, value);
    }
  } else if (strcmp(name, "enc_mode") == 0) {
    if (strcmp(value, "CS_SYMMALGMODE_LAPE") == 0) {
      config->enc_mode = CS_SYMMALGMODE_LAPE;
    } else {
      PARSE_UINT(config->enc_mode, value);
    }
  } else if (strcmp(name, "is_master") == 0) {
    PARSE_INT(config->is_master, value);
  } else {
    return 0; // Unknown name
  }
  return 1; // Success
}

static int parse_auth_server_config(auth_server_config_t *config,
                                    const char *name, const char *value) {
  if (strcmp(name, "port") == 0) {
    PARSE_INT(config->port, value);
  } else if (strcmp(name, "listen_ip") == 0) {
    PARSE_STRING(config->listen_ip, value, MAX_IP_ADDR_LEN);
  } else {
    return 0; // Unknown name
  }
  return 1; // Success
}
static int parse_logging_config(logging_config_t *config, const char *name,
                                const char *value) {
  if (strcmp(name, "log_to_file") == 0) {
    PARSE_BOOL(config->log_to_file, value);
  } else if (strcmp(name, "log_filename") == 0) {
    PARSE_STRING(config->log_filename, value, MAX_LOG_FILENAME_LEN);
  } else if (strcmp(name, "log_level") == 0) {
    config->log_level = parse_log_level(value);
  } else {
    return 0; // Unknown name
  }
  return 1; // Success
}

static int handler(void *user, const char *section, const char *name,
                   const char *value) {
  task_config_t *pconfig = (task_config_t *)user;
  char *value_copy = strdup(value);
  if (!value_copy) {
    fprintf(stderr, "Memory allocation error in config handler\n");
    return 0; // Allocation error
  }
  char *trimmed_value = trim_whitespace(value_copy);
  int result = 0; // 0 for error, 1 for success

  if (strcmp(section, "sitp_task") == 0) {
    result = parse_sitp_task_config(&pconfig->sitp_task, name, trimmed_value);
  } else if (strcmp(section, "csl_config") == 0) {
    result = parse_csl_config(&pconfig->csl, name, trimmed_value);
  } else if (strcmp(section, "auth_server") == 0) {
    result =
        parse_auth_server_config(&pconfig->auth_server, name, trimmed_value);
  } else if (strcmp(section, "logging") == 0) {
    result = parse_logging_config(&pconfig->logging, name, trimmed_value);
  } else {
    result = 1; // Section not recognized, successfully ignored
  }

  if (!result) {
    fprintf(stderr, "Unknown config name in section '%s': %s\n", section, name);
  }

  free(value_copy);
  return result;
}

static void set_default_config(task_config_t *config) {
  // SITP Task defaults
  PARSE_STRING(config->sitp_task.eth_dev, DEFAULT_ETH_DEV, MAX_ETH_DEV_LEN);
  config->sitp_task.mtu = DEFAULT_MTU;
  config->sitp_task.protocol = DEFAULT_PROTOCOL;
  config->sitp_task.local_id = DEFAULT_LOCAL_ID;
  config->sitp_task.remote_id = DEFAULT_REMOTE_ID;
  config->sitp_task.local_port = DEFAULT_LOCAL_PORT;
  config->sitp_task.remote_port = DEFAULT_REMOTE_PORT;
  config->sitp_task.pkt_trim_bytes = DEFAULT_PKT_TRIM_BYTES;
  config->sitp_task.sitp_q_cap = DEFAULT_SITP_Q_CAP;
  config->sitp_task.send_delay = DEFAULT_SEND_DELAY;
  config->sitp_task.queue_num = DEFAULT_QUEUE_NUM;

  // CSL Config defaults
  config->csl.device_no = DEFAULT_DEVICE_NO;
  PARSE_STRING(config->csl.pin, DEFAULT_PIN, MAX_PIN_LEN);
  PARSE_STRING(config->csl.enc_cert_name, DEFAULT_ENC_CERT_NAME,
               ENC_CERT_NAME_LEN);
  PARSE_STRING(config->csl.sign_cert_name, DEFAULT_SIGN_CERT_NAME,
               SIGN_CERT_NAME_LEN);
  PARSE_STRING(config->csl.iv, DEFAULT_IV, IV_LEN);
  config->csl.encrypt = DEFAULT_ENCRYPT;
  config->csl.symm_algo = DEFAULT_SYMM_ALGO;
  config->csl.asymm_algo = DEFAULT_ASYMM_ALGO;
  config->csl.enc_mode = DEFAULT_ENC_MODE;
  config->csl.is_master = DEFAULT_IS_MASTER;

  // Auth Server defaults
  config->auth_server.port = DEFAULT_AUTH_SERVER_PORT;
  PARSE_STRING(config->auth_server.listen_ip, DEFAULT_AUTH_SERVER_IP,
               MAX_IP_ADDR_LEN);

  // Logging defaults
  config->logging.log_to_file = DEFAULT_LOG_TO_FILE;
  PARSE_STRING(config->logging.log_filename, DEFAULT_LOG_FILENAME,
               MAX_LOG_FILENAME_LEN);
  config->logging.log_level = DEFAULT_LOG_LEVEL;
}

static void print_task_config(const task_config_t *config,
                              const char *filename) {
  printf("Configuration loaded from '%s':\n", filename);
  printf("  SITP Task Config:\n");
  printf("    eth_dev = %s\n", config->sitp_task.eth_dev);
  printf("    mtu = %d\n", config->sitp_task.mtu);
  printf("    protocol = 0x%x\n", config->sitp_task.protocol);
  printf("    local_id = 0x%x\n", config->sitp_task.local_id);
  printf("    remote_id = 0x%x\n", config->sitp_task.remote_id);
  printf("    local_port = 0x%x\n", config->sitp_task.local_port);
  printf("    remote_port = 0x%x\n", config->sitp_task.remote_port);
  printf("    pkt_trim_bytes = %u\n", config->sitp_task.pkt_trim_bytes);
  printf("    sitp_q_cap = %d\n", config->sitp_task.sitp_q_cap);
  printf("    send_delay = %u\n", config->sitp_task.send_delay);
  printf("    queue_num = %u\n", config->sitp_task.queue_num);

  printf("  CSL Config:\n");
  printf("    device_no = %d\n", config->csl.device_no);
  printf("    pin = %s\n", config->csl.pin);
  printf("    enc_cert_name = %s\n", config->csl.enc_cert_name);
  printf("    sign_cert_name = %s\n", config->csl.sign_cert_name);
  printf("    iv = %s\n", config->csl.iv);
  printf("    encrypt = %d\n", config->csl.encrypt);
  printf("    symm_algo = 0x%X\n", config->csl.symm_algo);
  printf("    asymm_algo = 0x%X\n", config->csl.asymm_algo);
  printf("    enc_mode = 0x%X\n", config->csl.enc_mode);
  printf("    is_master = %d\n", config->csl.is_master);

  printf("  Auth Server Config:\n");
  printf("    port = %d\n", config->auth_server.port);
  printf("    listen_ip = %s\n", config->auth_server.listen_ip);

  printf("  Logging Config:\n");
  printf("    log_to_file = %s\n", config->logging.log_to_file ? "true" : "false");
  printf("    log_filename = %s\n", config->logging.log_filename);
  printf("    log_level = %s\n", log_level_string(config->logging.log_level));
}

int load_task_config(const char *filename, task_config_t *config) {
  set_default_config(config);

  if (ini_parse(filename, handler, config) < 0) {
    printf("Can't load '%s', using default values for all settings.\n",
           filename);
    return -1; // Still using defaults, but indicate parse failure
  }

  print_task_config(config, filename);
  return 0; // Success
}

void initialize_global_config(const char *filename) {
  // Prevent re-initialization
  if (g_config_initialized) {
    return;
  }

  if (load_task_config(filename, &g_task_config_instance) == 0) {
    printf("Global configuration initialized successfully from '%s'.\n",
           filename);
  } else {
    printf("Failed to load global configuration from '%s'. Using defaults.\n",
           filename);
  }
  g_config_initialized = 1;
}

const task_config_t *get_global_config(void) {
  if (!g_config_initialized) {
    // This case should ideally not happen if initialize_global_config is called
    // at startup. Fallback to initializing with a default name if not done.
    fprintf(stderr, "Warning: Global config accessed before initialization. "
                    "Initializing with 'config.ini'.\n");
    initialize_global_config("config.ini");
  }
  return &g_task_config_instance;
}
