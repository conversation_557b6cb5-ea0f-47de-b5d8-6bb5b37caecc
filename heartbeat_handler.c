#include "heartbeat_handler.h"
#include "hex_utils.h"
#include <event2/bufferevent.h>
#include <stdio.h>

// Maximum heartbeat payload size
#define MAX_HEARTBEAT_PAYLOAD_SIZE 1024

int validate_heartbeat_payload(uint32_t host_len) {
  if (host_len > MAX_HEARTBEAT_PAYLOAD_SIZE) {
    fprintf(stderr,
            "Heartbeat message payload length %u exceeds maximum allowed size "
            "%d. Closing connection.\n",
            host_len, MAX_HEARTBEAT_PAYLOAD_SIZE);
    return -1;
  }
  return 0;
}

int process_heartbeat_message(struct bufferevent *bev,
                              const message_header_t *header,
                              unsigned char *payload, uint32_t host_len) {
  printf("Heartbeat message received.\n");

  if (host_len > 0 && payload) {
    print_payload(payload, host_len);
    printf("Sending heartbeat response (header + payload).\n");

    size_t header_size = sizeof(message_header_t);
    if (bufferevent_write(bev, header, header_size) != 0) {
      fprintf(stderr, "Failed to write heartbeat response header.\n");
      return -1;
    }

    if (bufferevent_write(bev, payload, host_len) != 0) {
      fprintf(stderr, "Failed to write heartbeat response payload.\n");
      return -1;
    }
  } else {
    printf("No payload in heartbeat. Not sending a response.\n");
  }

  return 0;
}