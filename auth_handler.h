#ifndef AUTH_HANDLER_H
#define AUTH_HANDLER_H

#include "message_common.h"
#include "oake.h"
#include <event2/bufferevent.h>
#include <stdint.h>

/**
 * @brief Validates authentication message payload length
 *
 * @param host_len Payload length in host byte order
 * @return 0 on success, -1 on failure
 */
int validate_auth_payload(uint32_t host_len);

/**
 * @brief Processes authentication request message (step 1 of OAKE handshake)
 *
 * @param bev Buffer event for the connection
 * @param header Message header
 * @param payload Message payload (can be NULL if host_len is 0)
 * @param host_len Payload length in host byte order
 * @param oake_data OAKE data structure for this IP
 * @param src_ip Source IP address string
 * @return 0 on success, -1 on failure
 */
int process_auth_request(struct bufferevent *bev,
                         const message_header_t *header, unsigned char *payload,
                         uint32_t host_len, oake_generic_data_t *oake_data,
                         const char *src_ip);

/**
 * @brief Processes authentication acknowledgment message (step 3 of OAKE
 * handshake)
 *
 * @param bev Buffer event for the connection
 * @param header Message header
 * @param payload Message payload (can be NULL if host_len is 0)
 * @param host_len Payload length in host byte order
 * @param oake_data OAKE data structure for this IP
 * @param src_ip Source IP address string
 * @return 0 on success, -1 on failure
 */
int process_auth_acknowledgment(struct bufferevent *bev,
                                const message_header_t *header,
                                unsigned char *payload, uint32_t host_len,
                                oake_generic_data_t *oake_data,
                                const char *src_ip);

/**
 * @brief Processes authentication message (routes to request or acknowledgment
 * handler)
 *
 * @param bev Buffer event for the connection
 * @param header Message header
 * @param payload Message payload (can be NULL if host_len is 0)
 * @param host_len Payload length in host byte order
 * @return 0 on success, -1 on failure
 */
int process_auth_message(struct bufferevent *bev,
                         const message_header_t *header, unsigned char *payload,
                         uint32_t host_len);

#endif /* AUTH_HANDLER_H */
