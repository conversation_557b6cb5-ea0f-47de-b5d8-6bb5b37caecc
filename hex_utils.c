#include "hex_utils.h"
#include <string.h>

// Function to print data in 2-byte hex groups with ASCII (similar to tcpdump
// -X)
void print_hex_ascii_line(const unsigned char *payload, int len, int offset) {
  int i;
  int hex_chars_count = 0;
  // Max hex characters for a 16-byte line:
  // 8 groups of "XXYY" (4 chars each) = 32 chars
  // 7 spaces between these 8 groups = 7 chars
  // Total = 39 chars
  const int max_hex_display_width = (16 / 2) * 4 + (16 / 2 - 1);

  // Offset
  printf("%04x: ", offset);

  // Hex, 2-byte groups
  for (i = 0; i < len; i += 2) {
    if (i > 0) {
      printf(" ");
      hex_chars_count++;
    }
    if (i + 1 < len) {
      printf("%02x%02x", payload[i], payload[i + 1]);
      hex_chars_count += 4;
    } else {
      // Odd byte at the end of the payload segment for this line
      printf(
          "%02x  ",
          payload[i]); // Print the byte and pad with two spaces for alignment
      hex_chars_count +=
          4; // Count as 4 (XX and two spaces) for consistent width calculation
    }
  }

  // Pad with spaces to align ASCII output
  for (i = hex_chars_count; i < max_hex_display_width; i++) {
    printf(" ");
  }

  printf("  "); // Separator between hex and ASCII

  // ASCII
  for (i = 0; i < len; i++) {
    if (isprint(payload[i]))
      printf("%c", payload[i]);
    else
      printf(".");
  }
  printf("\n");
}

// Function to print packet data
void print_payload(const unsigned char *payload, int len) {
  int len_rem = len;
  int line_width = 16; // Bytes per line
  int line_len;
  int offset = 0;
  const unsigned char *ch = payload;

  if (len <= 0)
    return;

  printf("Payload (len %d):\n", len);

  // Data fits on one line
  if (len <= line_width) {
    print_hex_ascii_line(ch, len, offset);
    return;
  }

  // Multiple lines
  for (;;) {
    // Compute current line length
    line_len = line_width;
    if (line_len > len_rem)
      line_len = len_rem;
    // Print line
    print_hex_ascii_line(ch, line_len, offset);
    // Processed line_len bytes
    len_rem = len_rem - line_len;
    ch = ch + line_len;
    offset = offset + line_len;
    // Check if we have line width chars or less
    if (len_rem <= 0) {
      break;
    }
  }
}

// Function to convert a single hex character to its integer value
static int hex_char_to_int(char c) {
  if (c >= '0' && c <= '9') {
    return c - '0';
  }
  if (c >= 'a' && c <= 'f') {
    return c - 'a' + 10;
  }
  if (c >= 'A' && c <= 'F') {
    return c - 'A' + 10;
  }
  return -1; // Invalid hex char
}

// Function to convert a hex string to a byte array
// Returns the number of bytes converted, or -1 on error (e.g., invalid hex
// string, insufficient buffer)
int hex_string_to_bytes(const char *hex_string, unsigned char *byte_array,
                        size_t max_bytes) {
  size_t len = strlen(hex_string);
  if (len % 2 != 0) {
    fprintf(stderr, "Hex string must have an even number of characters.\n");
    return -1; // Hex string must be even length
  }

  size_t byte_count = len / 2;
  if (byte_count > max_bytes) {
    fprintf(stderr,
            "Output buffer too small for hex string. Need %zu, got %zu\n",
            byte_count, max_bytes);
    return -1; // Output buffer too small
  }

  for (size_t i = 0; i < byte_count; i++) {
    int high_nibble = hex_char_to_int(hex_string[2 * i]);
    int low_nibble = hex_char_to_int(hex_string[2 * i + 1]);

    if (high_nibble == -1 || low_nibble == -1) {
      fprintf(stderr, "Invalid character in hex string: %c%c\n",
              hex_string[2 * i], hex_string[2 * i + 1]);
      return -1; // Invalid hex character
    }
    byte_array[i] = (unsigned char)((high_nibble << 4) | low_nibble);
  }
  return (int)byte_count;
}
