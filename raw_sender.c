#define _GNU_SOURCE
#include "raw_sender.h"
#include <pthread.h>
#include <stdio.h>
#include <unistd.h>

#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <sys/socket.h>

// Global raw socket and mutex for initialization
static pthread_mutex_t sender_init_mutex = PTHREAD_MUTEX_INITIALIZER;
static int raw_sock = -1;

// Helper function to initialize the raw socket if not already done
int initialize_sender(void) {
  pthread_mutex_lock(&sender_init_mutex);

  if (raw_sock != -1) {
    pthread_mutex_unlock(&sender_init_mutex);
    return 0;
  }

  if (raw_sock == -1) {
    raw_sock = socket(AF_INET, SOCK_RAW, IPPROTO_RAW);
    if (raw_sock < 0) {
      perror("raw_sender: socket(SOCK_RAW) error");
      pthread_mutex_unlock(&sender_init_mutex);
      return -1;
    }

    // Set IP_HDRINCL to tell the kernel that headers are included in the packet
    int on = 1;
    if (setsockopt(raw_sock, IPPROTO_IP, IP_HDRINCL, &on, sizeof(on)) < 0) {
      perror("raw_sender: setsockopt(IP_HDRINCL) error");
      close(raw_sock);
      raw_sock = -1;
      pthread_mutex_unlock(&sender_init_mutex);
      return -1;
    }
  }

  pthread_mutex_unlock(&sender_init_mutex);
  return 0;
}

// Function to get the raw socket file descriptor
// IMPORTANT: initialize_sender() must be successfully called before using this.
int get_raw_socket_fd(void) {
  // No mutex here as raw_sock is expected to be stable after initialization.
  // If initialize_sender hasn't been called or failed, raw_sock will be -1.
  return raw_sock;
}

void cleanup_raw_sender(void) {
  pthread_mutex_lock(&sender_init_mutex);

  if (raw_sock != -1) {
    close(raw_sock);
    raw_sock = -1;
    printf("raw_sender: Raw socket closed.\n");
  }
  pthread_mutex_unlock(&sender_init_mutex);
}
