```
iptables -D OUTPUT 1
iptables -I OUTPUT -p tcp -d ************ --dport 18020 -j NFQUEUE --queue-num 0
iptables -I INPUT -p tcp -s ************ --sport 18020 -j NFQUEUE --queue-num 0

iptables -I OUTPUT -p tcp -s ************ --sport 18020 -j NFQUEUE --queue-num 0
iptables -I INPUT -p tcp -d ************ --dport 18020 -j NFQUEUE --queue-num 0

iptables -I INPUT -p tcp -s ************ --sport 18020 -j NFQUEUE --queue-num 0
iptables -I OUTPUT -p tcp -d ************ --dport 18020 -j NFQUEUE --queue-num 0

iptables -I OUTPUT -p tcp -s ************ --sport 18020 -j NFQUEUE --queue-num 0
iptables -I INPUT -p tcp -d ************ --dport 18020 -j NFQUEUE --queue-num 0


iptables -I OUTPUT -p tcp -s ************ -d ************ -m multiport --ports 18020 -j NFQUEUE --queue-num 0
iptables -I INPUT -p tcp -s ************ -d ************ -m multiport --ports 18020 -j NFQUEUE --queue-num 0

```