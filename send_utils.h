#ifndef SEND_UTILS_H
#define SEND_UTILS_H

#include <stddef.h> // For size_t
#include <stdint.h>

/**
 * @brief Processes a packet (e.g., by padding) and enqueues it for sending via
 * SITP.
 *
 * The function retrieves padding configuration, creates a padded buffer,
 * and then enqueues this buffer into a dedicated send queue.
 * The actual sending is handled by a separate sender thread.
 * The original buffer passed to this function is not modified.
 * The padded buffer created internally is freed after enqueuing.
 *
 * @param buffer Pointer to the packet buffer to be processed and sent.
 * @param len Length of the packet buffer.
 * @return 0 if the packet was successfully enqueued, -1 on failure (e.g., queue
 * not initialized, padding error, enqueue error).
 */
int process_and_send_packet(uint8_t *buffer, size_t len);

/**
 * @brief Processes a packet (e.g., by padding) and enqueues it for sending via
 * SITP without encryption.
 *
 * This function has the same logic as process_and_send_packet but skips
 * the encryption step completely, always using the original plaintext buffer.
 * The function retrieves padding configuration, creates a padded buffer,
 * and then enqueues this buffer into a dedicated send queue.
 * The actual sending is handled by a separate sender thread.
 * The original buffer passed to this function is not modified.
 * The padded buffer created internally is freed after enqueuing.
 *
 * @param buffer Pointer to the packet buffer to be processed and sent.
 * @param len Length of the packet buffer.
 * @return 0 if the packet was successfully enqueued, -1 on failure (e.g., queue
 * not initialized, padding error, enqueue error).
 */
int process_and_send_packet_plaintext(uint8_t *buffer, size_t len);

/**
 * @brief Initializes the SITP sender queue and worker thread.
 *
 * This function should be called once at application startup.
 *
 * @param queue_capacity The maximum capacity of the send queue.
 * @return 0 on success, -1 on failure (e.g., queue creation failed, thread
 * creation failed).
 */
int init_sitp_sender(size_t queue_capacity);

/**
 * @brief Cleans up the SITP sender, stopping the thread and destroying the
 * queue.
 *
 * This function should be called once at application shutdown for graceful
 * termination.
 */
void cleanup_sitp_sender(void);

#endif // SEND_UTILS_H
