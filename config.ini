; Configuration for the SITP background task

[sitp_task]
eth_dev = eth1
mtu = 1400
protocol = 0x2000
local_id = 10
remote_id = 20
local_port = 12346
remote_port = 54322
pkt_trim_bytes = 32
sitp_q_cap = 1024
send_delay = 0
queue_num = 0

[csl_config]
device_no = 0
pin = 00000000
enc_cert_name = CAROOTCERTE_DEVENCCERT_ECC359
sign_cert_name = CAROOTCERTE_DEVSIGNCERT_ECC359
iv = 00112233445566778899AABBCCDDEEFF
encrypt = 0
symm_algo = 0x740000
asymm_algo = 0x170000
enc_mode = 0x0004
is_master = 0

[auth_server]
port = 7015
listen_ip = 0.0.0.0


[logging]
log_to_file = true
log_filename = tun_server.log
log_level = INFO
