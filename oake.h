#ifndef OAKE_H
#define OAKE_H

#include "include/bsk_api.h"
#include "ut/uthash.h"
#include <stddef.h>
#include <stdint.h>

#define OAKE_MAX_ID_LEN 64
#define OAKE_MAX_CERT_LEN 4096
#define OAKE_MAX_RND_LEN 128
#define OAKE_MAX_MAC_LEN 16
#define OAKE_MAX_IP_LEN 16  // Maximum length for IPv4 address string + null terminator

// Generic data structure for OAKE operations
typedef struct {
  void *ctx;
  uint8_t device_id[OAKE_MAX_ID_LEN];
  uint32_t device_id_len;
  uint8_t cert[OAKE_MAX_CERT_LEN];
  uint32_t cert_len;
  uint8_t rnd[OAKE_MAX_RND_LEN];
  uint32_t rnd_len;
  uint8_t mac[OAKE_MAX_MAC_LEN];
  uint32_t mac_len;
  void *remote_key;
  void *local_key;
  char src_ip[OAKE_MAX_IP_LEN];  // Source IP address string
  char dst_ip[OAKE_MAX_IP_LEN];  // Destination IP address string
} oake_generic_data_t;

// Function to get device ID - can be called externally
int oake_get_device_id(unsigned char *device_id_buf, uint32_t *device_id_len);

// OAKE initialization function
int oake_init(const unsigned char *device_id, unsigned int device_id_len,
              oake_generic_data_t **data_out);

// OAKE initialization function with provided context
// Similar to oake_init but uses the provided ctx directly instead of creating a new one
int oake_init_with_ctx(const unsigned char *device_id, unsigned int device_id_len,
                       void *ctx);

// OAKE create request function
int oake_create_request(oake_generic_data_t *data);

// OAKE create response function
int oake_create_response(oake_generic_data_t *data);

// OAKE create Acknowledgement function
int oake_create_acknowledgement(oake_generic_data_t *data);

// OAKE process acknowledgement function (handles B2 step)
int oake_process_acknowledgement(oake_generic_data_t *data);

// OAKE finalize session function
int oake_finalize_session(oake_generic_data_t *data);

// OAKE release resources function
int oake_release_resources(oake_generic_data_t *data);

// Serialization and deserialization functions for network transmission
// Note: ctx, remote_key, and local_key are excluded from serialization

// Calculate the size needed for serialized data
size_t oake_get_serialized_size(const oake_generic_data_t *data);

// Serialize oake_generic_data_t to buffer for network transmission
// Returns the number of bytes written, or -1 on error
int oake_serialize(const oake_generic_data_t *data, uint8_t *buffer,
                   size_t buffer_size);

// Deserialize buffer to oake_generic_data_t from network transmission
// Returns 0 on success, -1 on error
int oake_deserialize(const uint8_t *buffer, size_t buffer_size,
                     oake_generic_data_t *data);

// Hash table entry for storing oake_generic_data_t by IP
typedef struct {
    char ip[OAKE_MAX_IP_LEN];  // key (IP address)
    oake_generic_data_t data;  // value
    UT_hash_handle hh;         // makes this structure hashable
} oake_data_entry_t;

// Global hash table for storing OAKE data by IP
extern oake_data_entry_t *g_oake_data_by_ip;

// Function to get OAKE data based on master/slave configuration
oake_generic_data_t* get_oake_data_by_role(void);

// Hash table management functions
// Function to get OAKE data by IP address
oake_generic_data_t* oake_get_data_by_ip(const char *ip);

// Function to add/update OAKE data by IP address
int oake_set_data_by_ip(const char *ip, const oake_generic_data_t *data);

// Function to remove OAKE data by IP address
int oake_remove_data_by_ip(const char *ip);

// Function to clear all OAKE data from hash table
void oake_clear_all_data(void);

// Function to get the count of entries in the hash table
unsigned int oake_get_data_count(void);

#endif // OAKE_H
