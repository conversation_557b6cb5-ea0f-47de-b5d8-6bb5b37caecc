#ifndef HEARTBEAT_HANDLER_H
#define HEARTBEAT_HANDLER_H

#include "message_common.h"
#include <event2/bufferevent.h>
#include <stdint.h>

/**
 * @brief Validates heartbeat message payload length
 *
 * @param host_len Payload length in host byte order
 * @return 0 on success, -1 on failure
 */
int validate_heartbeat_payload(uint32_t host_len);

/**
 * @brief Processes heartbeat message
 *
 * @param bev Buffer event for the connection
 * @param header Message header
 * @param payload Message payload (can be NULL if host_len is 0)
 * @param host_len Payload length in host byte order
 * @return 0 on success, -1 on failure
 */
int process_heartbeat_message(struct bufferevent *bev,
                              const message_header_t *header,
                              unsigned char *payload, uint32_t host_len);

#endif /* HEARTBEAT_HANDLER_H */