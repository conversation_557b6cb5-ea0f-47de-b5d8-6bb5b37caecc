#include "config_handler.h"
#include "message_common.h"
#include "send_utils.h"
#include "ip_map.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

// Constants
#define IPV4_FIXED_LEN 16
#define MIN_TWO_IPV4_PAYLOAD_SIZE (2 * IPV4_FIXED_LEN)

// Function to validate IPv4 address format
static int validate_ipv4_address(const char *ip_str) {
  struct sockaddr_in sa;
  int result = inet_pton(AF_INET, ip_str, &(sa.sin_addr));

  if (result == 1) {
    printf("IPv4 address validation passed: %s\n", ip_str);
    return 0;  // Valid IPv4 address
  } else if (result == 0) {
    fprintf(stderr, "Invalid IPv4 address format: %s\n", ip_str);
    return -1;  // Invalid format
  } else {
    fprintf(stderr, "Error occurred during IPv4 address validation for: %s\n", ip_str);
    return -1;  // Error occurred
  }
}

// Function to extract and validate two IP addresses from payload
static int extract_and_validate_ips(const unsigned char *payload, uint32_t host_len,
                                   char *first_ip, char *second_ip,
                                   size_t *first_ip_len, size_t *second_ip_len) {
  if (host_len < MIN_TWO_IPV4_PAYLOAD_SIZE) {
    fprintf(stderr, "Config message payload too small: expected at least %d bytes for two IPv4 addresses, got %u\n",
            MIN_TWO_IPV4_PAYLOAD_SIZE, host_len);
    return -1;
  }

  if (!payload) {
    fprintf(stderr, "Payload is NULL\n");
    return -1;
  }

  // Copy first IP (first 16 bytes)
  memcpy(first_ip, payload, IPV4_FIXED_LEN);
  first_ip[IPV4_FIXED_LEN] = '\0';

  // Copy second IP (next 16 bytes)
  memcpy(second_ip, payload + IPV4_FIXED_LEN, IPV4_FIXED_LEN);
  second_ip[IPV4_FIXED_LEN] = '\0';

  // Get actual string lengths
  *first_ip_len = strnlen(first_ip, IPV4_FIXED_LEN);
  *second_ip_len = strnlen(second_ip, IPV4_FIXED_LEN);

  // Check if both IPs are non-empty
  if (*first_ip_len == 0 || *second_ip_len == 0) {
    fprintf(stderr, "Invalid payload format: one or both IPv4 addresses are empty\n");
    return -1;
  }

  printf("Parsed two IPv4 addresses: first='%s' (len=%zu), second='%s' (len=%zu)\n",
         first_ip, *first_ip_len, second_ip, *second_ip_len);

  // Validate both IP addresses
  if (validate_ipv4_address(first_ip) != 0) {
    fprintf(stderr, "First IP address validation failed: %s\n", first_ip);
    return -1;
  }

  if (validate_ipv4_address(second_ip) != 0) {
    fprintf(stderr, "Second IP address validation failed: %s\n", second_ip);
    return -1;
  }

  return 0;
}

// Function to send original packet data without modification
static int send_original_packet(const message_header_t *header, const unsigned char *payload, uint32_t payload_len) {
  // Construct complete packet with original header and payload
  size_t total_len = sizeof(message_header_t) + payload_len;
  uint8_t *complete_packet = malloc(total_len);
  if (!complete_packet) {
    fprintf(stderr, "Failed to allocate memory for complete packet\n");
    return -1;
  }

  // Copy original header
  memcpy(complete_packet, header, sizeof(message_header_t));

  // Copy original payload without any modification
  memcpy(complete_packet + sizeof(message_header_t), payload, payload_len);

  // Send complete packet via plaintext method
  printf("Sending original complete packet (header + original payload) with total length: %zu bytes\n", total_len);
  int result = process_and_send_packet_plaintext(complete_packet, total_len);

  // Free memory
  free(complete_packet);

  if (result != 0) {
    fprintf(stderr, "Failed to send original config message packet\n");
    return -1;
  }

  printf("Original config message packet sent successfully\n");
  return 0;
}

int validate_config_payload(uint32_t host_len) {
  // Check config-specific payload size limits
  if (host_len > MAX_CONFIG_PAYLOAD_SIZE) {
    fprintf(stderr,
            "Config payload length %u exceeds maximum allowed size %d\n",
            host_len, MAX_CONFIG_PAYLOAD_SIZE);
    return -1;
  }

  printf("Config payload validation passed: length=%u\n", host_len);
  return 0;
}

int process_config_message(struct bufferevent *bev,
                           const message_header_t *header,
                           const unsigned char *payload, uint32_t host_len) {
  printf("Processing config message: payload_len=%u\n", host_len);

  char first_ip[IPV4_FIXED_LEN + 1] = {0};
  char second_ip[IPV4_FIXED_LEN + 1] = {0};
  size_t first_ip_len, second_ip_len;

  // Extract and validate IP addresses from payload
  if (extract_and_validate_ips(payload, host_len, first_ip, second_ip, &first_ip_len, &second_ip_len) != 0) {
    return -1;
  }

  // Add IP mapping pair (first_ip -> second_ip)
  if (add_ip_mapping(first_ip, second_ip) != 0) {
    fprintf(stderr, "Failed to add IP mapping: %s -> %s\n", first_ip, second_ip);
    return -1;
  }
  printf("Successfully added IP mapping: %s -> %s\n", first_ip, second_ip);

  // Send original packet data without modification
  if (send_original_packet(header, payload, host_len) != 0) {
    return -1;
  }

  return 0;
}