#include "config_handler.h"
#include "ip_map.h"
#include "log.h"
#include "message_common.h"
#include "send_utils.h"
#include <arpa/inet.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Constants
#define IPV4_FIXED_LEN 16
#define MIN_TWO_IPV4_PAYLOAD_SIZE (2 * IPV4_FIXED_LEN)

// Function to validate IPv4 address format
static int validate_ipv4_address(const char *ip_str) {
  struct sockaddr_in sa;
  int result = inet_pton(AF_INET, ip_str, &(sa.sin_addr));

  if (result == 1) {
    log_debug("IPv4 address validation passed: %s", ip_str);
    return 0;  // Valid IPv4 address
  } else if (result == 0) {
    log_error("Invalid IPv4 address format: %s", ip_str);
    return -1;  // Invalid format
  } else {
    log_error("Error occurred during IPv4 address validation for: %s", ip_str);
    return -1;  // Error occurred
  }
}

// Function to extract and validate two IP addresses from payload
static int extract_and_validate_ips(const unsigned char *payload, uint32_t host_len,
                                   char *first_ip, char *second_ip,
                                   size_t *first_ip_len, size_t *second_ip_len) {
  if (host_len < MIN_TWO_IPV4_PAYLOAD_SIZE) {
    log_error("Config message payload too small: expected at least %d bytes for two IPv4 addresses, got %u",
              MIN_TWO_IPV4_PAYLOAD_SIZE, host_len);
    return -1;
  }

  if (!payload) {
    log_error("Payload is NULL");
    return -1;
  }

  // Copy first IP (first 16 bytes)
  memcpy(first_ip, payload, IPV4_FIXED_LEN);
  first_ip[IPV4_FIXED_LEN] = '\0';

  // Copy second IP (next 16 bytes)
  memcpy(second_ip, payload + IPV4_FIXED_LEN, IPV4_FIXED_LEN);
  second_ip[IPV4_FIXED_LEN] = '\0';

  // Get actual string lengths
  *first_ip_len = strnlen(first_ip, IPV4_FIXED_LEN);
  *second_ip_len = strnlen(second_ip, IPV4_FIXED_LEN);

  // Check if both IPs are non-empty
  if (*first_ip_len == 0 || *second_ip_len == 0) {
    log_error("Invalid payload format: one or both IPv4 addresses are empty");
    return -1;
  }

  log_debug("Parsed two IPv4 addresses: first='%s' (len=%zu), second='%s' (len=%zu)",
            first_ip, *first_ip_len, second_ip, *second_ip_len);

  // Validate both IP addresses
  if (validate_ipv4_address(first_ip) != 0) {
    log_error("First IP address validation failed: %s", first_ip);
    return -1;
  }

  if (validate_ipv4_address(second_ip) != 0) {
    log_error("Second IP address validation failed: %s", second_ip);
    return -1;
  }

  return 0;
}

// Function to send original packet data without modification
static int send_original_packet(const message_header_t *header, const unsigned char *payload, uint32_t payload_len) {
  // Construct complete packet with original header and payload
  size_t total_len = sizeof(message_header_t) + payload_len;
  uint8_t *complete_packet = malloc(total_len);
  if (!complete_packet) {
    log_error("Failed to allocate memory for complete packet");
    return -1;
  }

  // Copy original header
  memcpy(complete_packet, header, sizeof(message_header_t));

  // Copy original payload without any modification
  memcpy(complete_packet + sizeof(message_header_t), payload, payload_len);

  // Send complete packet via plaintext method
  log_debug("Sending original complete packet (header + original payload) with total length: %zu bytes", total_len);
  int result = process_and_send_packet_plaintext(complete_packet, total_len);

  // Free memory
  free(complete_packet);

  if (result != 0) {
    log_error("Failed to send original config message packet");
    return -1;
  }

  log_info("Original config message packet sent successfully");
  return 0;
}

int validate_config_payload(uint32_t host_len) {
  // Check config-specific payload size limits
  if (host_len > MAX_CONFIG_PAYLOAD_SIZE) {
    log_error("Config payload length %u exceeds maximum allowed size %d",
              host_len, MAX_CONFIG_PAYLOAD_SIZE);
    return -1;
  }

  log_debug("Config payload validation passed: length=%u", host_len);
  return 0;
}

int process_config_message(struct bufferevent *bev,
                           const message_header_t *header,
                           const unsigned char *payload, uint32_t host_len) {
  log_info("Processing config message: payload_len=%u", host_len);

  char first_ip[IPV4_FIXED_LEN + 1] = {0};
  char second_ip[IPV4_FIXED_LEN + 1] = {0};
  size_t first_ip_len, second_ip_len;

  // Extract and validate IP addresses from payload
  if (extract_and_validate_ips(payload, host_len, first_ip, second_ip, &first_ip_len, &second_ip_len) != 0) {
    return -1;
  }

  // Add IP mapping pair (first_ip -> second_ip)
  if (add_ip_mapping(first_ip, second_ip) != 0) {
    log_error("Failed to add IP mapping: %s -> %s", first_ip, second_ip);
    return -1;
  }
  log_info("Successfully added IP mapping: %s -> %s", first_ip, second_ip);

  // Send original packet data without modification
  if (send_original_packet(header, payload, host_len) != 0) {
    return -1;
  }

  return 0;
}
