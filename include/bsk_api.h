/*!
 *  Copyright (C), 2023-2030, <PERSON><PERSON><PERSON>.
 *  \file       bsk_api.h
 *  \author     <PERSON><PERSON><PERSON><PERSON>
 *  \version    V6.0.2310.0609
 *  \date       2023-10-06
 *  \note       define some macro,structure and functions exported by api6.0.
 *
 *
 */

#ifndef _SM_API_H_
#define _SM_API_H_

#include "sm_api_type.h"
#include "sm_algo.h"

/* ///////////////////////////////////////////////////////////////////////// */
/* define API version */
/* ///////////////////////////////////////////////////////////////////////// */
#ifdef WINMIX
#define SM_API_VERSION "1, 0, 2023, 1006-win32@64"
#else
#define SM_API_VERSION "V0.2.2.1-2025-01-21" \
					   "build" __DATE__
#endif
/* ///////////////////////////////////////////////////////////////////////// */

/* ///////////////////////////////////////////////////////////////////////// */
/*
 * define current Hash process mode is normal
 * used by Hash related API
 */
/* ///////////////////////////////////////////////////////////////////////// */
#define SMH_DEV_MODE_SD 0
#define SMH_DEV_MODE_CP 1

/* ///////////////////////////////////////////////////////////////////////// */
/*
 * define some interface correlation constant
 */
/* ///////////////////////////////////////////////////////////////////////// */
#define CS_NORMAL 0x00	   // open the device with share mode
#define CS_EXCLUSIIVE 0x02 // open the device with exclusive mode(not support)
/*GetDevice Status : StatusIndex */
#define CS_STATUS_MANAGE 0x01				   // management state
#define CS_STATUS_AUTHMEDIA 0x02			   // certified medium state
#define CS_STATUS_DEVICEINFO_RESTTRYCOUNT 0x03 //
#define CS_STATUS_BUG 0x04					   //
#define CS_STATUS_KEYDESTROY 0x05			   //
#define CS_STATUS_LOGIN 0x06				   //
#define CS_STATUS_SWITCHTOWORKINGMODE 0x07	   //
#define CS_STATUS_RESCONFIGMODE 0x08		   //

#define CS_STATUS_ALG 0x0A		// alg 状态
#define CS_STATUS_ALG_PARA 0x0B // alg para状态
#define CS_STATUS_KEY 0x0C		// alg key状态
#define CS_STATUS_RAND 0x0D		// RAND状态

/*CS_STATUS_MANAGE : status*/
#define CS_STATUS_MANAGE_BIRTH 0x01	  //
#define CS_STATUS_MANAGE_INIT 0x02	  //
#define CS_STATUS_MANAGE_READY 0x03	  //
#define CS_STATUS_MANAGE_WORKING 0x04 //
/*CS_STATUS_AUTHMEDIA : status*/
#define CS_STATUS_AUTHMEDIA_NOMEDIA 0x00  //
#define CS_STATUS_AUTHMEDIA_ADMIN 0x01	  //
#define CS_STATUS_AUTHMEDIA_OPERATOR 0x02 //
#define CS_STATUS_AUTHMEDIA_UNKNOWN 0x03  //

/*CS_STATUS_BUG : status*/
#define CS_STATUS_BUG_YES 0x01 //
#define CS_STATUS_BUG_NO 0x00  //
/*CS_STATUS_KEYDESTROY : status*/
#define CS_STATUS_KEYDESTROY_NO 0x00	 //
#define CS_STATUS_KEYDESTROY_FIRST 0x01	 //
#define CS_STATUS_KEYDESTROY_SECOND 0x02 //
/*CS_STATUS_LOGIN : status*/
#define CS_STATUS_LOGIN_NO 0x00		  //
#define CS_STATUS_LOGIN_ADMIN 0x01	  //
#define CS_STATUS_LOGIN_OPERATOR 0x02 //
/*CS_STATUS_SWITCHTOWORKINGMODE : status*/
#define CS_STATUS_SWITCHTOWORKINGMODE_OFFLINE 0x00 //
#define CS_STATUS_SWITCHTOWORKINGMODE_ONLINE 0x01  //
/*CS_STATUS_RESCONFIGMODE : status*/
#define CS_STATUS_RESCONFIGMODE_PRESET 0x00	  //
#define CS_STATUS_RESCONFIGMODE_DOWNLOAD 0x01 //

#define CS_USERADMIN 0x01	 // admin
#define CS_USEROPERATOR 0x02 //

#define CS_SYMM_ENC 0x01 // Symmetric Encrypt
#define CS_SYMM_DEC 0x00 // Symmetric Decrypt

// the flag of symmtric
#define FLAG_RELEASEHKEY 0x01	 // release key handle
#define FLAG_NOTRELEASEHKEY 0x00 // unrelease key handle

// the cert
#define CERT_FIELD_TYPE 0x00070000
#define CERT_FIELD_SUBJECT_NAME CERT_FIELD_TYPE + 0x00000001	 // 主题信息
#define CERT_FIELD_CERT_VERSION CERT_FIELD_TYPE + 0x00000002	 // X509 版本号
#define CERT_FIELD_SERIALNUM CERT_FIELD_TYPE + 0x00000003		 // 序列号
#define CERT_FIELD_SIGN_ALG CERT_FIELD_TYPE + 0x00000004		 // 签名算法
#define CERT_FIELD_SIGN_VALUE CERT_FIELD_TYPE + 0x00000005		 // 签名值
#define CERT_FIELD_AUTHORITY_NAME CERT_FIELD_TYPE + 0x00000006	 // 颁发机构
#define CERT_FIELD_NOTBEFORE_TIME CERT_FIELD_TYPE + 0x00000007	 // 生效时间"2023-10-23 01:22：30"
#define CERT_FIELD_NOTAFTER_TIME CERT_FIELD_TYPE + 0x00000008	 // 过期时间
#define CERT_FIELD_PUBLIC_KEY CERT_FIELD_TYPE + 0x00000009		 // 公钥
#define CERT_FIELD_KEY_USEAGE CERT_FIELD_TYPE + 0x0000000A		 // 密钥用途
#define CERT_FIELD_SUBJECT_ALTNAME CERT_FIELD_TYPE + 0x0000000B	 // 别名（扩展名）
#define CERT_FIELD_BASIC_CONSTRAINT CERT_FIELD_TYPE + 0x0000000C // 基本约束
#define CERT_FIELD_CERT_ALG CERT_FIELD_TYPE + 0x0000000D		 // 证书级别
#define CERT_FIELD_SUBJECT_KEYID CERT_FIELD_TYPE + 0x0000000E	 // 主体密钥标识符
#define CERT_FIELD_AUTHORITY_KEYID CERT_FIELD_TYPE + 0x0000000F	 // 颁发机构密钥标识符
#define CERT_FIELD_CRL_DISTPOINT CERT_FIELD_TYPE + 0x00000011	 // CRL发布点
#define CERT_FIELD_CERTINFO CERT_FIELD_TYPE + 0x00000012		 // 证书体（签名明文）

typedef struct ECC_ADDITIONALINFO_st
{
	SM_INT Version;		 // 版本，从1开始
	PSM_BYTE Random;	 // 随机数
	SM_INT RandomLength; // 随机数长度
} ECC_ADDITIONALINFO;

/*! struct SM_DEVICE_STATUS. */
typedef struct _SM_DEVICE_STATUS
{
	SM_INT iTestResult;
	SM_INT iDeviceMode;
} SM_DEVICE_STATUS, *PSM_DEVICE_STATUS;

/*! struct SM_BLOB_KEY. */
typedef struct _SM_BLOB_DATA
{
	/*!
	 * A member variable.
	 * The length of data
	 */
	SM_UINT uiDataLen;
	/*!
	 * A member variable.
	 * The pointer of data
	 */
	PSM_BYTE pbyData;
} SM_BLOB_KEY, *PSM_BLOB_KEY;
typedef struct _SM_BLOB_CONTEXT
{
	/*!
	 * A member variable.
	 */
	SM_UINT uiCtxIndex;
	/*!
	 * A member variable.
	 */
	SM_ALGORITHM_TYPE AlgType;
} SM_BLOB_CONTEXT, *PSM_BLOB_CONTEXT;

typedef struct log_T
{
	unsigned int log_time; // 日志时间
	int log_type;		   // 日志类型 0：:管理日志 1:异常日志；
	char log[64];		   // 日志内容
} __attribute__((packed)) log_t;

/*
 *    ECC OAKE ctc
 */
typedef struct _ARRANGE_CTX
{
	SM_UINT asymalgno;	   // 非对称算法号,用于判断oake中使用算法种类，281,359,521
	SM_UINT symalgno;	   // 对称算法号，用于设置密钥协商得到的密钥在哪个对称算法使用
	SM_UINT hashalgno;	   // 杂凑算法号，用于设置密钥协商得到的密钥在哪个杂凑算法使用
	SM_UINT skhandle;	   // 私钥句柄，就是M300的ecc_sn
	SM_UINT sharekey;	   // 与服务分发设备进行基于公钥的密钥协商时所需的共享密钥句柄，需要时由M300填充
	SM_UINT local_ID_len;  // 本端ID长度
	SM_UINT romote_ID_len; // 对端ID长度
	SM_BYTE local_id[20];  //
	SM_BYTE romote_id[20];
	SM_BYTE local_pka[136]; // 本端公钥明文
} ARRANGE_CTX, *PARRANGE_CTX;

/* 口令验证情况分类 */
typedef enum
{
	RCE_PASSWORD_OK,				   /* 口令正确或口令修改成功 */
	RCE_USER_ERROR_NO = 0x80000060,	   /* 账号错误，不存在 */
	RCE_USER_ERROR_LEN,				   /* 用户名长度错误 */
	RCE_USER_ERROR,					   /* 用户名错误 */
	RCE_PASSWORD_ERROR_LEN,			   /* 口令长度错误 */
	RCE_PASSWORD_ERROR_DATA,		   /* 口令内容错误 */
	RCE_PASSWORD_ERROR_LOCKED,		   /* 口令已锁死 */
	RCE_PASSWORD_ERROR_MANAGE,		   /*管理员认证失败 */
	RCE_PASSWORD_ERROR_NO_KEY,		   /* 开机认諩失败 */
	RCE_PASSWORD_MMJ_ERROR,			   /*密码机硬件故障 */
	RCE_PASSWORD_NOINJECT_DESTROY,	   /*密码机未注入或已销毁*/
	RCE_PASSWORD_NOMANAGER_BAR,		   /*开机棒不存在*/
	RCE_PASSWORD_WRITE_MANAGER_FAIL,   /*写开机棒失败*/
	RCE_PASSWORD_READ_MANAGER_FAIL,	   /*读开机棒失败*/
	RCE_PASSWORD_MANAGER_BAR_DATA_ERR, /*开机棒数据错误*/
	RCE_PASSWORD_WRITE_FLASH_ERR	   /*写数据到MCUfash错误*/
} rc_password_e_t;

/***************************************
 * 验证用户口令 *************************
 ***************************************/
typedef struct
{
	SM_UINT user_type; /* 角色类型：1：系统管理员；2：安全保密管理员；3：审计管理员*/
	SM_UINT user_id;   /* 用户名唯一ID，隶属于各个角色，每个角色的ID：0-9，ID不能重复*/
	SM_UINT ul;		   /* 用户长度 */
	SM_BYTE user[32];  /* 用户名称 */
	SM_UINT pwdlen;	   /* 用户口令长度 */
	SM_BYTE pwd[32];   /* 用户名称 */
} rc_password_t;

/***************************************
 * 修改用户口令 *************************
 ***************************************/
/* 用户口令修改输入参数 */
typedef struct
{
	SM_UINT user_type;	/* 角色类型：1：系统管理员；2：安全保密管理员；3：审计管理员*/
	SM_UINT user_id;	/* 用户名唯一ID，隶属于各个角色，每个角色的ID：0-9，ID不能重复*/
	SM_UINT ul;			/* 用户长度 */
	SM_BYTE user[32];	/* 用户名称 */
	SM_UINT pwdlen;		/* 用户口令长度 */
	SM_BYTE pwd[32];	/* 用户名称 */
	SM_UINT newpwdlen;	/* 用户口令长度 */
	SM_BYTE newpwd[32]; /* 用户名称 */
} rc_modify_password_t;

typedef struct
{
	SM_UINT ps;			 // 验证结果 0：成功 非零：失败
	SM_UINT first_staus; // 0：不需要修改口令，1：需要修改口令。
} rc_veri_password_status_t;

typedef struct
{
	SM_UINT result;
	SM_UINT status;		  // 密码状态转移,出厂态
	SM_UINT alg_status;	  // 算法逻辑状态
	SM_UINT para_status;  // 算法参数状态
	SM_UINT key_status;	  // 密钥参数状态
	SM_UINT rand_status;  // 噪声源状态
	SM_UINT login_status; // 登录状态
	SM_UINT error_status; // 故障状态
} api_status;

typedef struct
{
	SM_UINT result;
	SM_UINT num; // 资源类型数量
	SM_UINT len; // 结构体长度
	SM_BYTE data[0];
} rco_quary_resource;

typedef struct
{
	SM_UINT res_type; // 资源类型,1算法，2：参数，3：密钥
	SM_BYTE name[20]; //
	SM_UINT SN;
	SM_BYTE starttime[20];
	SM_BYTE endtime[20];
} res_alginfo_res;

/*----------------------------------------
 * 密码资源信息查询
 *----------------------------------------*/
typedef struct
{
	SM_UINT res_type; // 资源类型,1算法，2：参数，3：对称密钥 4：私钥；5：证书
	SM_BYTE name[20]; //
	SM_BYTE sn[32];	  // 版本号，用
	SM_BYTE starttime[20];
	SM_BYTE endtime[20];
	SM_UINT len;
	SM_UINT status; // 启用状态，0：正常 1：未启用，
} res_keyinfo_res;

#if 0
typedef struct {
	SM_UINT res_type;//资源类型,1算法，2：参数，3：密钥
	GJB8019_para_head info; //名称，类型
}rco_para_info;


typedef struct{
	SM_UINT res_type;//资源类型,1算法，2：参数，3：密钥
	SM_BYTE keysn[32];//密钥序列号
	SM_BYTE key_type[2];//密钥类别
	SM_BYTE starttime[20];//密钥启用时间
	SM_BYTE endtime[20];//密钥失效时间
	SM_BYTE keylen[4];//密钥长度
}Asym_key_info;

typedef struct {
	SM_UINT res_type;//资源类型,1算法，2：参数，3：密钥
	SM_BYTE keysn[14];//密钥序列号
	SM_BYTE key_type[2];//密钥类别
	SM_BYTE key_use;//密钥用途
	SM_BYTE key_area[4];//区号
	SM_BYTE key_batchnum[4];//批次
	SM_BYTE bak;//主备
	SM_BYTE res[32];//自定义描述
	SM_BYTE starttime[20];//密钥启用时间
	SM_BYTE changetime[4];//更换周期
	SM_BYTE singlelen[2];//单组密钥长度
}Sym_key_info;

typedef struct{
	SM_UINT res_type;//资源类型,1算法，2：参数，3：密钥,4:证书
	SM_BYTE certsn[32];// 证书序号序列号
	SM_BYTE type[2];//类别
	SM_BYTE starttime[20];//启用时间
	SM_BYTE endtime[20];//失效时间
	SM_BYTE keylen[4];//密钥长度
}cert_info;
#endif

/* ///////////////////////////////////////////////////////////////////////// */
/* define the error code */
/* ///////////////////////////////////////////////////////////////////////// */
/*! 00, Operation success */
#define CSR_OK 0

#define CSR_BASIC_ERR 0x80000000
/*! normal(0x80000001-0x80000024) */
/*!0x80000001, Unknown error */
#define CSR_UNKNOWN_ERR CSR_BASIC_ERR | 1
/*!0x80000002, unsupported interface call error */
#define CSR_UNSUPPORTED_FUNC CSR_BASIC_ERR | 2
/*! 0x80000003, Communicate with interface failed */
#define CSR_COMM_ERR CSR_BASIC_ERR | 3
/*! 0x80000004, the operation module is not responding */
#define CSR_HARDWARE_ERR CSR_BASIC_ERR | 4
/*! 0x80000005, open device failed */
#define CSR_OPENDEVICE_ERR CSR_BASIC_ERR | 5
/*! 0x80000006, No permission */
#define CSR_PERMISSION_DENIED CSR_BASIC_ERR | 6
/*! 0x80000007, error key id */
#define CSR_INEXIST_KEY CSR_BASIC_ERR | 7
/*! 0x80000008, error algorithm type */
#define CSR_UNSUPPORTED_ALG CSR_BASIC_ERR | 8
/*! 0x80000009, error algorithm mode type */
#define CSR_UNSUPPORTED_ALGMOD CSR_BASIC_ERR | 9
/*! 0x8000000a, ECC Encrypt err */
#define CSR_PUBKEY_OPER_ERR CSR_BASIC_ERR | 10
/*! 0x8000000b, ECC Decrypt err */
#define CSR_PRIKEY_OPER_ERR CSR_BASIC_ERR | 11
/*! 0x8000000c, ECC Signature err */
#define CSR_SIGN_ERR CSR_BASIC_ERR | 12
/*! 0x8000000d, ECC Verify err */
#define CSR_VERIFY_ERR CSR_BASIC_ERR | 13
/*! 0x8000000e, Symmetric algorithm operation err */
#define CSR_SYMM_OPER_ERR CSR_BASIC_ERR | 14
/*! 0x8000000f, key err */
#define CSR_KEY_ERR CSR_BASIC_ERR | 15
/*! 0x80000010, duplication of name err */
#define CSR_MULTINAME_ERR CSR_BASIC_ERR | 16
/*! 0x80000011,No such file or directory */
#define CSR_NOSUCHFILEORDIR CSR_BASIC_ERR | 17
/*! 0x80000012, file already exists */
#define CSR_FILEEXISTS CSR_BASIC_ERR | 18
/*! 0x80000013, No such Device */
#define CSR_NOSUCHDEVICE CSR_BASIC_ERR | 19
/*! 0x80000014, is not a directory */
#define CSR_NOTADIR CSR_BASIC_ERR | 20
/*! 0x80000015, invalid parameter */
#define CSR_INVALIDPARAMETER CSR_BASIC_ERR | 21
/*! 0x80000016, operation timeout */
#define CSR_TIMEOUT CSR_BASIC_ERR | 22
/*! 0x80000017, communication error */
#define CSR_COMMUNICATION_ERR CSR_BASIC_ERR | 23
/*! 0x80000018, open the library failed */
#define CSR_OPENDLLFAILED CSR_BASIC_ERR | 24
/*! 0x80000019, invalid library */
#define CSR_INVALIDDLL CSR_BASIC_ERR | 25
/*! 0x8000001a, library not found */
#define CSR_DLLNOTFOUND CSR_BASIC_ERR | 26
/*! 0x8000001b, invalid handle */
#define CSR_INVALIDHANDLE CSR_BASIC_ERR | 27
/*! 0x8000001c, invalid data */
#define CSR_INVALIDDATA CSR_BASIC_ERR | 28
/*! 0x8000001d, error data length */
#define CSR_BADLENGTH CSR_BASIC_ERR | 29
/*! 0x8000001e, write error */
#define CSR_WRITEFAULT CSR_BASIC_ERR | 30
/*! 0x8000001f, read error */
#define CSR_READFAULT CSR_BASIC_ERR | 31
/*! 0x80000020, invalid pin */
#define CSR_INVALIDPASSWORD CSR_BASIC_ERR | 32
/*! 0x80000021, open failed */
#define CSR_OPENFAILED CSR_BASIC_ERR | 33
/*! 0x80000022, not login */
#define CSR_NOLOGIN CSR_BASIC_ERR | 34
/*! 0x80000023, login failed */
#define CSR_LOGINFAILED CSR_BASIC_ERR | 35
/*! 0x80000024, system error */
#define CSR_SYSTEM_ERR CSR_BASIC_ERR | 36
/*! 0x80000025, error device handle */
#define CSR_ERR_DEVICE_HANDLE CSR_BASIC_ERR | 37
/*! 0x80000026, error checksum */
#define CSR_ERR_CHECKSUM CSR_BASIC_ERR | 38
/*! 0x80000027, error malloc */
#define CSR_ERR_MALLOC CSR_BASIC_ERR | 39

/*! 0x80000028, error malloc */
#define CSR_ERR_INPARA CSR_BASIC_ERR | 40

/*! 0x80000029, error malloc */
#define CSR_ERR_CERTOUTTIME CSR_BASIC_ERR | 41

#define SM_ERR_TIMEOUT 0x5550
#define SM_ERR_CMDNO 0x5555
/* ///////////////////////////////////////////////////////////////////////// */
/* define the interface functions */
/* ///////////////////////////////////////////////////////////////////////// */
#ifdef __cplusplus
extern "C"
{
#endif

	/* General functions   */
	/* G1. CPCI_GetDeviceNum */
	/*!
	 *  A function, get device number that can be accessed.
	 *  \param puiDevNum            [out] the number pointer of the device.
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_GetDeviceNum(
		unsigned int *puiDevNum);
	/* G2. CPCI_GetErrorString */
	/*!
	 *  A function, convert the error value to string.
	 *  \param uiErrCode            [in]  the error value.
	 *  \param bChinese             [in]  the type of return string, Chinese or English.
	 *  \return a string of error value.
	 *  \warning none.
	 */
	PSM_CHAR
	CPCI_GetErrorString(
		SM_RV uiErrCode,
		SM_BOOL bChinese);
	/* G3. CPCI_GetAPIVersion */
	/*!
	 *  A function, get API version.
	 *  \return a string of version.
	 *  \warning none.
	 */
	PSM_CHAR
	CPCI_GetAPIVersion();
	/* G4. CPCI_GetFPGAVersion */
	/*!
	 *  A function, get FPGA version.
	 *  \param phDevice             [in] the handle pointer of the device.
	 *  \param pbyFPGAVer     [out]  the version of FPGA.
	 *  \return a string of version.
	 *  \warning none.
	 */
	SM_RV CPCI_GetFPGAVersion(
		SM_DEVICE_HANDLE phDevice, /* in  */
		PSM_BYTE pbyFPGAVer);	   /* out  */

	/* G4. CPCI_OpenDevice */
	/*!
	 *  A function, open the specified device.
	 *  \param pphDevice             [out] the handle pointer of the device.
	 *  \param DeviceNo       [in]  the device ID.
	 *  \param Flags          [in]  the mode of open, exclusive or share.
	 *  \param AppID          [in]   no use
	 *  \param VMID           [in]   no use
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_OpenDevice(
		void **pphDevice,
		int DeviceNo,
		int AppID,
		int VMID,
		int Flags);

	/* G5. CPCI_CloseDevice */
	/*!
	 *  A function, close the specified device.
	 *  \param phDevice              [in]  the handle of the device.
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_CloseDevice(
		void *phDevice);

	/* G6. CPCI_GetDeviceStatus */
	/*!
	 *  A function, get the index of the specified device.
	 *  \param phDevice              [in]  the handle of the device.
	 *  \param StatusIndex     [in]  the device status index.
	 *  \param pStatus         [out] the pointer of the device status.
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_GetDeviceStatus(
		void *phDevice,		/* in  */
		api_status *pStatus /* out */
	);

	/*G7. CPCI_LoginDevice */
	/*!
	 *  A function, login.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param LoginType            [in]  the type of the user 1-admin 2-user
	 *  \param pPin                 [in]  the pointer of the pin
	 *  \param pParam               [in]  reserved parameter
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_LoginDevice(
		void *phDevice,		  /* in  */
		int LoginType,		  /* in  */
		char *pPin,			  /* in  */
		unsigned int iPinLen, /* in */
		void *pParam		  /* in  */
	);
	/*G8. CPCI_LogoutDevice */
	/*!
	 *  A function, logout.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param LoginType            [in]  the type of the user 1-admin 2-user
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */

	SM_RV CPCI_LogoutDevice(
		void *phDevice,
		int LoginType);
	/* G9. CPCI_ReadDataFromDevice */
	/*!
	 *  A function, read data from device.
	 *  \param phDevice                   [in]  the handle of the device.
	 *  \param DataName             [in] the name of the read data.
	 *  \param Data                [out] the address of the data to be read.
	 *  \param DataLen             [in] the length of the data to be read.
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_ReadDataFromDevice(
		void *phDevice,
		const char *DataName,
		unsigned char *pData,
		int *pDataLen);
	/* G10. CPCI_GetDeviceID */
	/*!
	 *  A function, read data from device.
	 *  \param phDevice                   [in]  the handle of the device.
	 *  \param pbyDeviceID             [out] the pointer of the device id.
	 *  \param piDeviceIDLen           [out] the pointer of the device id length.
	 *  \param Flags             [in] reserved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_GetDeviceID(
		void *phDevice,				/* in  */
		unsigned char *pbyDeviceID, /* out  */
		int *piDeviceIDLen,			/* out  */
		int Flags					/* Reserved  */
	);
	__attribute__((visibility("default"))) int CPCI_GetMNID(
		void *phDevice,				/* in  */
		unsigned char *pbyDeviceID, /* out  */
		int *piDeviceIDLen,			/* out  */
		int Flags					/* Reserved  */
	);
	/* G12. CPCI_SetTime */
	__attribute__((visibility("default"))) int CPCI_SetTime(
		void *phDevice,		   /* in  */
		unsigned int LocalTime /* in  */
	);
	/* G13. CPCI_GetTime */
	__attribute__((visibility("default"))) int CPCI_GetTime(
		void *phDevice,			/* in  */
		unsigned int *LocalTime /* out  */
	);
	/* G13. CPCI_Destroy */
	__attribute__((visibility("default"))) int CPCI_Destroy(
		void *phDevice,	  /* in  */
		unsigned int Type /* in  */
	);

	/* G15. CPCI_QueryResource */
	__attribute__((visibility("default"))) int CPCI_QueryResource(
		void *phDevice,			   /* in  */
		unsigned int Type,		   /* in  */
		unsigned char *pbyDataOut, /* out  */
		unsigned int *pwOutLen	   /* out  */
	);
	/* G16. CPCI_LoadDataToDevice */
	__attribute__((visibility("default"))) int CPCI_LoadDataToDevice(
		void *phDevice,
		const char *DataName,
		unsigned char *pData,
		unsigned int pDataLen);

	/* D1. CPCI_CommTest */
	/*!
	 *  A function, the communication test to x1000.
	 *  \param phDevice               [in]  the handle of the device.
	 *  \param Flags            [in]  the flag to where
	 *  \param pbyDataIn,		[in]  the buffer of in data
	 *  \param pwInLen,			[in]  the data length of in data
	 *  \param pbyDataOut,		[in]  the buffer of out data
	 *  \param pwOutLen         [in]  the data length of out data
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */

	int CPCI_CommTest(
		void *phDevice, /* in  */
		unsigned short usFlags,
		unsigned char *pbyDataIn,
		unsigned short *pwInLen,
		unsigned char *pbyDataOut,
		unsigned short *pwOutLen);
	/* D2. CPCI_LoopBackTest */
	/*!
	 *  A function, the communication test to x1000.
	 *  \param phDevice               [in]  the handle of the device.
	 *  \param pbyDataIn,		[in]  the buffer of in data
	 *  \param pwInLen,			[in]  the data length of in data
	 *  \param pbyDataOut,		[in]  the buffer of out data
	 *  \param pwOutLen         [in]  the data length of out data
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_LoopBackTest(
		void *phDevice,			   /* in  */
		unsigned char *pbyDataIn,  /* in  */
		unsigned short *pwInLen,   /* in  */
		unsigned char *pbyDataOut, /* in  */
		unsigned short *pwOutLen   /* in  */
	);

	/* Key functions          */
	/* K1. CPCI_GenRandom */
	/*!
	 *  A function, generate random data.
	 *  \param phDevice                   [in]  the handle of the device
	 *	\param Length			    [in]  the length of the random data
	 *  \param pData                [out] the pointer of random data
	 *  \param Flags                [in]   reserved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_GenRandom(
		void *phDevice,
		int Length,
		unsigned char *pData,
		int Flag);
	/*K2 CPCI_GenKeyPair*/
	/*!
	 *  A function, generate ECC key pair.
	 *  \param phDevice                   [in]  the handle of the device
	 *	\param AlgNo			    [in]  the Algorithm No
	 *  \param hKey                 [out] the pointer of key handle
	 *  \param PubKey               [out] the pointer of public key
	 *  \param Flags                [in] reserved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_GenKeyPair(
		void *phDevice,		   /* in */
		int AlgNo,			   /* in */
		void **hKey,		   /* out */
		unsigned char *PubKey, /* out */
		int Flags			   /* in */
	);
	/* K3. CPCI_CreateKey */
	/*!
	 *  A function, create symmetric or mac algorithm key
	 *  \param phDevice                   [in]  the handle of the device
	 *	\param AlgNo			    [in]  the Algorithm No
	 *  \param pKeyData             [in] the pointer of public key
	 *  \param DataLen              [in] the data length of public key
	 *  \param hKey                 [out] the pointer of key handle
	 *  \param Flags                [in] reserved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_CreateKey(
		void *phDevice,			 /* in  */
		int AlgNo,				 /* in  */
		unsigned char *pKeyData, /* in  */
		int DataLen,			 /* in */
		void **hKey,			 /* out  */
		int Flags				 /* Reserved  */
	);
	/* K4 CPCI_OpenKey */
	int CPCI_OpenKey(
		void *phDevice,			 /* in  */
		int AlgNo,				 /* in  */
		void *phKey,			 /* in  */
		const char *pKeyName,	 /* in  */
		int keyID,				 /* in  */
		int Type,				 /* in  */
		unsigned char *pKeyData, /* in  */
		int DataLen,			 /* in  */
		void **hKey,			 /* out */
		unsigned char *PubKey,	 /* out */
		int Flags				 /* Reserved  */
	);
	/* K5. CPCI_ReleaseKey */
	/*!
	 *  A function, release symmetric or mac algorithm key
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param hKey                 [in] the pointer of key handle
	 *  \param Flags                [in] reserved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_ReleaseKey(
		void *phDevice, /* in  */
		void *hKey,		/* in  */
		int Flags		/* Reserved  */
	);
	/* P1. CPCI_SymmInit */
	/*!
	 *  A function, symmetric algorithm init.
	 *  \param phDevice                   [in]  the handle of the device
	 *	\param AlgNo			    [in]  the Algorithm No
	 *  \param hKey                 [in]  key id
	 *  \param pIV                  [in] the pointer of iv
	 *  \param EncType              [in] the flag to encrypt of decrypt
	 *  \param pSymmCtx             [out] the pointer of the context data
	 *  \param Flag         		[in]  check whether the key flag is clear
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_SymInit(
		void *phDevice,		  /* in */
		unsigned int AlgNo,	  /* in */
		void *hKey,			  /* in */
		unsigned char *pIV,	  /* in */
		unsigned int EncType, /* in */
		void **ppSymmCtx,	  /* out */
		unsigned int Flag	  /* in */
	);
	/* P2. CPCI_SymUpdate */
	/*!
	 *  A function,symmetric algorithm update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param pSymmCtx             [in] the pointer of context data
	 *  \param InLen                [in] the data length of plain data
	 *  \param pInData              [in] the pointer of plain
	 *  \param bLast                [in] the last packet flag
	 *  \param OutLen               [out] the pointer of the length to Cipher data
	 *  \param pOutData             [out] the pointer of the Cipher data
	 *  \param pMAC                 [out] the pointer of the Cipher data
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_SymUpdate(
		void *phDevice,			 /* in */
		void *ppSymmCtx,		 /* in */
		unsigned int InLen,		 /* in */
		unsigned char *pInData,	 /* in */
		unsigned int bLast,		 /* in */
		unsigned int *OutLen,	 /* out */
		unsigned char *pOutData, /* out */
		unsigned char *pMAC		 /* in/out */
	);
	/* P3. CPCI_SymFinal */
	/*!
	 *  A function, symmetric algorithm final.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param pSymmCtx             [in] the pointer of context data
	 *  \param pMAC                 [out] the pointer of the Cipher data
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_SymFinal(
		void *phDevice,		/* in */
		void *pSymmCtx,		/* in */
		unsigned char *pMAC /* in/out */
	);

	int CPCI_SymSingle(
		void *phDevice,
		unsigned int AlgNo,
		unsigned int EncType,
		void *hKey,
		unsigned char *pIV, /* in */
		unsigned char *pbyDataIn,
		unsigned int DataInLen,
		unsigned char *pbyDataOut,
		unsigned int *DataOutLen,
		unsigned char *pMAC);

	/* P5. CPCI_CreateDigest */
	/*!
	 *  A function, init hash.
	 *  \param phDevice                   [in]  the handle of the device
	 *	\param AlgNo			    [in]  the Algorithm No
	 *  \param ModeType             [in]  the mode of the algorithm
	 *  \param IV                   [in] the pointer of iv
	 *  \param hKey                 [in]  key id
	 *  \param pDigestCtx           [out] the pointer of the context data
	 *  \param Flags         		[in]  check whether the key flag is clear
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_CreateDigest(
		void *phDevice,		   /* in */
		unsigned int AlgNo,	   /* in 0:primary algo 1: other*/
		unsigned int ModeType, /* in 0: hash 1: hmac */
		unsigned char *IV,	   /* in */
		void *hKey,			   /* in */
		void **hDigestCtx,	   /* out */
		unsigned int Flags	   /* in */
	);

	/* P6. CPCI_DigestData */
	/*!
	 *  A function, hash update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param pDigestCtx           [in] the pointer of the context data
	 *  \param pInput               [in]  the pointer of the plain data
	 *  \param InputLen             [in]  the data length of plain data
	 *  \param hInputKey            [in]  key id
	 *  \param bLast                [in]  a sign of whether it is the last packet
	 *  \param Flags         		[in]  resved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_DigestData(
		void *phDevice,		   /* in */
		void *hDigestCtx,	   /* in */
		unsigned char *pInput, /* in */
		unsigned int InputLen, /* in */
		void *hInputKey,	   /* in */
		unsigned int bLast,	   /* in */
		unsigned int Flags	   /* in */
	);

	/* P7. CPCI_FinishDigest */
	/*!
	 *  A function, hash update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param pDigestCtx           [in] the pointer of the context data
	 *  \param hKey                 [in]  the pointer of the key
	 *  \param pOutput              [out]  the pinter of hash data
	 *  \param OutputLen            [in/out]  the data length of hash
	 *  \param Flags         		[in]  resved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_FinishDigest(
		void *phDevice,			 /* in */
		void *hDigestCtx,		 /* in */
		void **hKey,			 /* out */
		unsigned char *pOutput,	 /* out */
		unsigned int *OutputLen, /* in/out */
		unsigned int Flags		 /* resved */
	);

	/* P8. CPCI_AsymmEnc */
	/*!
	 *  A function, hash update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param AlgNo			      [in]  the Algorithm No
	 *  \param PubKey                    [in]  the pointer of the public key
	 * \param pInput                       [in]  the pinter of plain data
	 *  \param InLen                        [in]  the data length of plain
	 *  \param pOutput                     [out]  the pinter of cipher data
	 *  \param OutLen                      [in/out]  the data length of cipher
	 *  \param ExtraData                  [in] the pointer of the struct extradata
	 *  \param Flag         		      [in]  resved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_AsymmEnc(
		void *phDevice,				   /* _In_  */
		int AlgNo,					   /* _In_  */
		unsigned char *PubKey,		   /* _In_  */
		unsigned char *pInput,		   /* _In_  */
		int InLen,					   /* _In_  */
		unsigned char *pOutput,		   /* _Out_  */
		int *OutLen,				   /* _Out_  */
		ECC_ADDITIONALINFO *ExtraData, /* _In_  */
		int Flag					   /* _In_  */
	);
	/* P9. CPCI_AsymmEnc */
	/*!
	 *  A function, hash update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param AlgNo			      [in]  the Algorithm No
	 *  \param phDecKey                  [in]  the pointer of the key pair
	 * \param pInput                       [in]  the pinter of plain data
	 *  \param InLen                        [in]  the data length of plain
	 *  \param pOutput                     [out]  the pinter of cipher data
	 *  \param OutLen                      [in/out]  the data length of cipher
	 *  \param Flag         		      [in]  resved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_AsymmDec(
		void *phDevice,			/* _In_  */
		int AlgNo,				/* _In_  */
		void *phDecKey,			/* _In_  */
		unsigned char *pInput,	/* _In_  */
		int InLen,				/* _In_  */
		unsigned char *pOutput, /* _Out_  */
		int *OutLen,			/* _Out_  */
		int Flag				/* _In_  */
	);
	/* P10. CPCI_AsymmSignature */
	/*!
	 *  A function, hash update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param AlgNo			      [in]  the Algorithm No
	 * \param pHashValue                       [in]  the pinter of plain data
	 *  \param HashLen                        [in]  the data length of plain
	 *  \param hPriKey    [in]  the pointer of the key
	 *  \param pSigndData                     [out]  the pinter of cipher data
	 *  \param SigndDataLen                  [in/out]  the data length of cipher
	 *  \param ExtraData                  [in] the pointer of the struct extradata
	 *  \param Flag         		      [in]  resved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_SignHash(
		void *phDevice,			   /* _In_  */
		int AlgNo,				   /* _In_  */
		unsigned char *pHashValue, /* _In_  */
		int HashLen,			   /* _In_  */
		void *hPriKey,			   /* _In_  */
		unsigned char *pSigndData, /* _Out_  */
		int *SigndDataLen,		   /* _Out_  */
		ECC_ADDITIONALINFO *ExtraData,
		int Flag /* _In_  */
	);
	/* P11 CPCI_VerifySignature */
	/*!
	 *  A function, hash update.
	 *  \param phDevice                   [in]  the handle of the device
	 *  \param AlgNo			      [in]  the Algorithm No
	 * \param pHashValue                       [in]  the pinter of plain data
	 *  \param HashLen                        [in]  the data length of plain
	 *  \param PubKey                       [in] the pointer of the public key
	 *  \param pSigndData                     [out]  the pinter of cipher data
	 *  \param SigndDataLen                  [in/out]  the data length of cipher
	 *  \param Flag         		      [in]  resved
	 *  \return 0-ok, !0-fail.
	 *  \warning none.
	 */
	int CPCI_VerifySignature(
		void *phDevice,			   /* _In_  */
		int AlgNo,				   /* _In_  */
		unsigned char *pHashValue, /* _In_  */
		int HashLen,			   /* _In_  */
		unsigned char *PubKey,	   /* _In_  */
		unsigned char *pSigndData, /* _Out_  */
		int *SigndDataLen,		   /* _Out_  */
		int Flag				   /* _In_  */
	);
	int CPCI_OAKE_ECC_Init(
		void *phDevice,
		int iSymmAlgNo,
		int iHashAlgNo,
		int iAsymmAlgNo,
		unsigned char *pDevIdB,
		unsigned int DevIdBLen,
		int Type,
		int ServerFlag,
		void **pCtx,
		unsigned char *pbyOutData,
		int Flag);
	int CPCI_OAKE_ECC_A1(
		void *phDevice, /* in */
		void *pCtx,		/* in */
		char *pRa,		/* out */
		int *pRaLen,	/* out */
		int Flag		/* resverd */
	);
	int CPCI_OAKE_ECC_B1(
		void *phDevice, /* in */
		void *pCtx,		/* in */
		char *pRa,		/* in */
		int RaLen,		/* in */
		char *pPkA,		/* in */
		int PkALen,		/* in */
		char *pRb,		/* out */
		int *pRbLen,	/* out */
		char *pMACB,	/* out */
		int *pMACBLen,	/* out */
		int Flag		/* reserved */
	);
	int CPCI_OAKE_ECC_A2(
		void *phDevice,		  /* in */
		void *pCtx,			  /* in */
		char *pRb,			  /* in */
		int RbLen,			  /* in */
		char *pPkB,			  /* in */
		int PkBLen,			  /* in */
		char *pMACB,		  /* in */
		int MACBLen,		  /* in */
		char *pMACA,		  /* out */
		int *pMACALen,		  /* out */
		void **hRemoteKeyCtx, /* out */
		void **hLocalKeyCtx,  /* out */
		int Flag			  /* reserved */
	);
	int CPCI_OAKE_ECC_B2(
		void *phDevice,		  /* in */
		void *pCtx,			  /* in */
		char *pMACA,		  /* in */
		int MACALen,		  /* in */
		void **hRemoteKeyCtx, /* out */
		void **hLocalKeyCtx,  /* out */
		int Flag			  /* reserved */
	);
	int CPCI_OAKE_ECC_Final(
		void *phDevice,
		void *pCtx,
		int Flag);
	int CPCI_CertParse(
		void *phDevice,		 /* in */
		unsigned char *Cert, /* in */
		unsigned int clen,	 /* in */
		unsigned int field,	 /* in */
		unsigned char *data, /* out */
		unsigned int *dlen	 /* out */
	);
	int CPCI_CertVerify(
		void *phDevice,
		unsigned char *cert,
		unsigned int certlen,
		unsigned char *cacert,
		unsigned int calen,
		unsigned char *crl,
		unsigned int crlen);
	int CPCI_DigitalEnvelopeEncrypt(
		void *phDevice,				 /* in */
		unsigned char *cert,		 /* in */
		unsigned int certlen,		 /* in */
		unsigned int AlgNo,			 /* in */
		unsigned int mlen,			 /* in */
		unsigned char *message,		 /* in */
		unsigned int *cipher_iv_len, /* out */
		unsigned char *cipher_text,	 /* out */
		unsigned int *m_cipher_len,	 /* out */
		unsigned char *m_cipher_data /* out */
	);
	int CPCI_DigitalEnvelopeDecrypt(
		void *phDevice,				  /* in */
		unsigned int AlgNo,			  /* in */
		unsigned int cipher_iv_len,	  /* in */
		unsigned char *cipher_text,	  /* in */
		unsigned int m_cipher_len,	  /* in */
		unsigned char *m_cipher_data, /* in */
		unsigned int *mlen,			  /* out */
		unsigned char *message		  /* out */
	);

	int CPCI_SoftWareUpdate(
		void *phDevice,			/* in */
		unsigned int tpye,		/* in */
		unsigned int total_len, /* in */
		unsigned int flag,		/* in */
		unsigned char *soft_bin /* in */
	);

	int CPCI_PassWordDevice(
		void *phDevice,						   /* in  */
		rc_password_t *pPin,				   /* in password*/
		rc_veri_password_status_t *pPin_status /*out  result   */
	);
	int CPCI_ModifyPassWordDevice(
		void *phDevice,						   /* in  */
		rc_modify_password_t *pPin,			   /* in password*/
		rc_veri_password_status_t *pPin_status /* out  result  */
	);
	int CPCI_AddUSer(
		void *phDevice,						   /* in  */
		rc_password_t *pPin,				   /* in password*/
		rc_veri_password_status_t *pPin_status /*  out  result */
	);

	int CPCI_DeleteUser(
		void *phDevice,						   /* in  */
		rc_password_t *pPin,				   /* in password*/
		rc_veri_password_status_t *pPin_status /*  out  result*/
	);

	int CPCI_GetLog(
		void *phDevice,	 /*in*/
		log_t *log_info, /* out log*/
		int *log_num	 /*  out  log_num*/
	);

#ifdef __cplusplus
}
#endif

#endif
