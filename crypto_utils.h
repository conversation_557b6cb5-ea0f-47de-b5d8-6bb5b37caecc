#ifndef CRYPTO_UTILS_H
#define CRYPTO_UTILS_H

#include "include/bsk_api.h" // For SM_RV and other types
#include "include/sm_algo.h" // For algorithm identifiers like SM_ALGO_SM4_CBC

// Custom error codes for the crypto utility
#define CRYPTO_UTIL_ERR_BASE 0x90000000
#define CRYPTO_UTIL_ERR_NOT_INITIALIZED (CRYPTO_UTIL_ERR_BASE | 1)
#define CRYPTO_UTIL_ERR_CONFIG_LOAD (CRYPTO_UTIL_ERR_BASE | 2) // For future use
#define CRYPTO_UTIL_ERR_INVALID_PARAM (CRYPTO_UTIL_ERR_BASE | 3)
#define CRYPTO_UTIL_ERR_MAC_BUFFER_SMALL (CRYPTO_UTIL_ERR_BASE | 4)
#define CRYPTO_UTIL_ERR_PADDING_BUFFER_OVERFLOW (CRYPTO_UTIL_ERR_BASE | 5)
#define CRYPTO_UTIL_ERR_OTHER (CRYPTO_UTIL_ERR_BASE | 6) // General/other error

/**
 * @brief Encrypts data using the underlying symmetric encryption function
 * (e.g., CPCI_SymSingle) with PKCS#7 padding.
 *
 * This function handles device initialization checks and uses a pre-configured
 * initialization vector (IV) and algorithm. The specific IV and algorithm
 * (e.g., SM_ALGO_SM4_CBC) are determined by internal configuration.
 *
 * The function automatically applies PKCS#7 padding to the input data:
 * - Pads to the next 16-byte boundary
 * - If data is already a multiple of 16 bytes, adds a full 16-byte padding
 * block
 * - Padding bytes are set to the padding length value (e.g., 0x01 for 1 byte,
 * 0x10 for 16 bytes)
 *
 * @param device_handle Handle to the symmetric key. This handle is typically
 *                             obtained from key creation or opening operations
 * (e.g., CPCI_CreateKey or CPCI_OpenKey).
 * @param plaintext_data Pointer to the input data (plaintext) to be encrypted.
 * @param plaintext_data_len Length of the input plaintext data in bytes.
 * @param ciphertext_buffer Buffer to store the resulting encrypted data
 * (ciphertext). Must be large enough to hold padded data (plaintext_len + up to
 * 16 padding bytes).
 * @param ciphertext_buffer_len Pointer to an unsigned integer. On input, this
 * must specify the allocated size of `ciphertext_buffer`. On successful
 * encryption, it will be updated with the actual length of the ciphertext
 *                              written to `ciphertext_buffer`.
 * @param mac_output_buffer Buffer to store the generated Message Authentication
 * Code (MAC).
 * @return CSR_OK on success.
 * @return CRYPTO_UTIL_ERR_NOT_INITIALIZED if the cryptographic device/module is
 * not initialized.
 * @return CRYPTO_UTIL_ERR_INVALID_PARAM if any input parameters are invalid
 * (e.g., null pointers, zero lengths).
 * @return CRYPTO_UTIL_ERR_PADDING_BUFFER_OVERFLOW if `ciphertext_buffer` is too
 * small for the padded data.
 * @return CRYPTO_UTIL_ERR_MAC_BUFFER_SMALL if `mac_output_buffer` is too small
 * for the generated MAC.
 * @return Other error codes from the underlying cryptographic API (bsk_api.h)
 * for specific crypto failures.
 */
SM_RV crypto_encrypt_data(void *device_handle,
                          const unsigned char *plaintext_data,
                          unsigned int plaintext_data_len,
                          unsigned char *ciphertext_buffer,
                          unsigned int *ciphertext_buffer_len,
                          unsigned char *mac_output_buffer);

/**
 * @brief Decrypts data using the underlying symmetric decryption function
 * (e.g., CPCI_SymSingle) and verifies MAC, with PKCS#7 padding removal.
 *
 * This function handles device initialization checks and uses a pre-configured
 * initialization vector (IV) and algorithm. The specific IV and algorithm
 * (e.g., SM_ALGO_SM4_CBC) are determined by internal configuration.
 * The function also performs MAC verification and automatically removes PKCS#7
 * padding.
 *
 * The function automatically removes PKCS#7 padding from the decrypted data:
 * - Validates padding bytes match the expected PKCS#7 format
 * - Removes padding to return the original plaintext length
 * - Returns error if padding is invalid
 *
 * @param device_handle Handle to the symmetric key. This handle is typically
 *                             obtained from key creation or opening operations
 * (e.g., CPCI_CreateKey or CPCI_OpenKey).
 * @param ciphertext_data Pointer to the input data (ciphertext) to be
 * decrypted.
 * @param ciphertext_data_len Length of the input ciphertext data in bytes.
 * @param plaintext_buffer Buffer to store the resulting decrypted data
 * (plaintext) without padding.
 * @param plaintext_buffer_len Pointer to an unsigned integer. On input, this
 * must specify the allocated size of `plaintext_buffer`. On successful
 * decryption, it will be updated with the actual length of the unpadded
 * plaintext written to `plaintext_buffer`.
 * @param mac_input_data Pointer to the Message Authentication Code (MAC)
 * provided for verification against the decrypted data.
 * @param mac_input_len Length of the `mac_input_data` in bytes.
 * @return CSR_OK on successful decryption and MAC verification.
 * @return CRYPTO_UTIL_ERR_NOT_INITIALIZED if the cryptographic device/module is
 * not initialized.
 * @return CRYPTO_UTIL_ERR_INVALID_PARAM if any input parameters are invalid
 * (e.g., null pointers, zero lengths, invalid padding).
 * @return CRYPTO_UTIL_ERR_PADDING_BUFFER_OVERFLOW if `plaintext_buffer` is too
 * small for the unpadded data.
 * @return Other error codes from the underlying cryptographic API (bsk_api.h)
 * for specific crypto failures, including errors related to MAC verification
 * failure (e.g., CPCI_SymSingle might return a specific error if decryption is
 * successful but MAC verification fails).
 */
SM_RV crypto_decrypt_data(void *device_handle,
                          const unsigned char *ciphertext_data,
                          unsigned int ciphertext_data_len,
                          unsigned char *plaintext_buffer,
                          unsigned int *plaintext_buffer_len,
                          const unsigned char *mac_input_data,
                          unsigned int mac_input_len);

#endif // CRYPTO_UTILS_H
