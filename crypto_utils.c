#include "crypto_utils.h"
#include "config_loader.h"
#include "device_manager.h"
#include "hex_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

SM_RV crypto_encrypt_data(void *key_handle, const unsigned char *plaintext_data,
                          unsigned int plaintext_data_len,
                          unsigned char *ciphertext_buffer,
                          unsigned int *ciphertext_buffer_len,
                          unsigned char *mac_output_buffer) {

  if (!key_handle || !plaintext_data || !ciphertext_buffer ||
      !ciphertext_buffer_len || !mac_output_buffer) {
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }
  if (*ciphertext_buffer_len == 0) {
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }

  void *device_handle = device_get_api_handle();
  if (!device_handle) {
    return CRYPTO_UTIL_ERR_NOT_INITIALIZED;
  }

  const task_config_t *config = get_global_config();
  if (!config) {
    fprintf(stderr,
            "Error: Global config not loaded in crypto_encrypt_data.\n");
    return CRYPTO_UTIL_ERR_OTHER;
  }
  unsigned int alg_no = config->csl.symm_algo;

  unsigned char iv_bytes[16];
  int converted_len =
      hex_string_to_bytes(config->csl.iv, iv_bytes, sizeof(iv_bytes));
  if (converted_len != sizeof(iv_bytes)) {
    fprintf(stderr,
            "Error converting IV from hex string or incorrect IV length. "
            "Expected %zu, got %d\n",
            sizeof(iv_bytes), converted_len);
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }

  // PKCS#7 padding implementation
  // Calculate padding length - always pad to next 16-byte boundary
  // If already aligned to 16 bytes, add a full 16-byte block
  unsigned int padding_len = 16 - (plaintext_data_len % 16);
  if (padding_len == 0) {
    padding_len = 16; // Add full block if already aligned
  }

  unsigned int padded_data_len = plaintext_data_len + padding_len;

  // Check if output buffer is large enough for padded data
  if (*ciphertext_buffer_len < padded_data_len) {
    return CRYPTO_UTIL_ERR_PADDING_BUFFER_OVERFLOW;
  }

  // Create padded data buffer
  unsigned char *padded_data = (unsigned char *)malloc(padded_data_len);
  if (!padded_data) {
    return CRYPTO_UTIL_ERR_OTHER;
  }

  // Copy original data
  memcpy(padded_data, plaintext_data, plaintext_data_len);

  // Add PKCS#7 padding bytes
  for (unsigned int i = 0; i < padding_len; i++) {
    padded_data[plaintext_data_len + i] = (unsigned char)padding_len;
  }

  unsigned int enc_mode = CS_SYMM_ENC | config->csl.enc_mode;
  SM_RV result =
      CPCI_SymSingle(device_handle, alg_no, enc_mode, key_handle, iv_bytes,
                     padded_data, padded_data_len, ciphertext_buffer,
                     ciphertext_buffer_len, mac_output_buffer);

  // Clean up allocated memory
  free(padded_data);

  return result;
}

SM_RV crypto_decrypt_data(void *key_handle,
                          const unsigned char *ciphertext_data,
                          unsigned int ciphertext_data_len,
                          unsigned char *plaintext_buffer,
                          unsigned int *plaintext_buffer_len,
                          const unsigned char *mac_input_data,
                          unsigned int mac_input_len) {

  if (!key_handle || !ciphertext_data || !plaintext_buffer ||
      !plaintext_buffer_len || !mac_input_data) {
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }
  if (*plaintext_buffer_len == 0) {
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }

  void *device_handle = device_get_api_handle();
  if (!device_handle) {
    return CRYPTO_UTIL_ERR_NOT_INITIALIZED;
  }

  const task_config_t *config = get_global_config();
  if (!config) {
    fprintf(stderr,
            "Error: Global config not loaded in crypto_decrypt_data.\n");
    return CRYPTO_UTIL_ERR_OTHER;
  }
  unsigned int alg_no = config->csl.symm_algo;

  unsigned char iv_bytes[16];
  int converted_len =
      hex_string_to_bytes(config->csl.iv, iv_bytes, sizeof(iv_bytes));
  if (converted_len != sizeof(iv_bytes)) {
    fprintf(stderr,
            "Error converting IV from hex string or incorrect IV length in "
            "decrypt. Expected %zu, got %d\n",
            sizeof(iv_bytes), converted_len);
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }

  if (mac_input_len == 0) {
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }
  unsigned char
      mac_buffer[16]; // Assuming MAC length is appropriate (e.g., 16 for SM4)
  if (mac_input_len > sizeof(mac_buffer)) {
    return CRYPTO_UTIL_ERR_MAC_BUFFER_SMALL;
  }
  memcpy(mac_buffer, mac_input_data, mac_input_len);

  // Create temporary buffer for decryption (with padding)
  unsigned char *temp_plaintext = (unsigned char *)malloc(ciphertext_data_len);
  if (!temp_plaintext) {
    return CRYPTO_UTIL_ERR_OTHER;
  }
  unsigned int temp_plaintext_len = ciphertext_data_len;

  unsigned int dec_mode = CS_SYMM_DEC | config->csl.enc_mode;
  SM_RV result =
      CPCI_SymSingle(device_handle, alg_no, dec_mode, key_handle, iv_bytes,
                     (unsigned char *)ciphertext_data, ciphertext_data_len,
                     temp_plaintext, &temp_plaintext_len, mac_buffer);

  if (result != CSR_OK) {
    free(temp_plaintext);
    return result;
  }

  // Remove PKCS#7 padding
  if (temp_plaintext_len == 0) {
    free(temp_plaintext);
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }

  // Get padding length from last byte
  unsigned char padding_len = temp_plaintext[temp_plaintext_len - 1];

  // Validate padding
  if (padding_len == 0 || padding_len > 16 ||
      padding_len > temp_plaintext_len) {
    free(temp_plaintext);
    return CRYPTO_UTIL_ERR_INVALID_PARAM;
  }

  // Verify all padding bytes are correct
  for (unsigned int i = temp_plaintext_len - padding_len;
       i < temp_plaintext_len; i++) {
    if (temp_plaintext[i] != padding_len) {
      free(temp_plaintext);
      return CRYPTO_UTIL_ERR_INVALID_PARAM;
    }
  }

  // Calculate actual plaintext length without padding
  unsigned int actual_plaintext_len = temp_plaintext_len - padding_len;

  // Check if output buffer is large enough
  if (*plaintext_buffer_len < actual_plaintext_len) {
    free(temp_plaintext);
    return CRYPTO_UTIL_ERR_PADDING_BUFFER_OVERFLOW;
  }

  // Copy unpadded data to output buffer
  memcpy(plaintext_buffer, temp_plaintext, actual_plaintext_len);
  *plaintext_buffer_len = actual_plaintext_len;

  free(temp_plaintext);
  return CSR_OK;
}
