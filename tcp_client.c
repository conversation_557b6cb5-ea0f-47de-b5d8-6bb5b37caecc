#include "tcp_client.h"
#include <arpa/inet.h>
#include <errno.h>
#include <event2/buffer.h>
#include <event2/bufferevent.h>
#include <event2/event.h>
#include <netinet/in.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <time.h>
#include <unistd.h>

// Internal function declarations
static void on_read_cb(struct bufferevent *bev, void *ctx);
static void on_event_cb(struct bufferevent *bev, short what, void *ctx);
static void reconnect_timer_cb(evutil_socket_t fd, short what, void *ctx);
static void schedule_reconnect(tcp_client_t *client);
static void *event_loop_thread(void *arg);
static int tcp_client_connect_internal(tcp_client_t *client);

tcp_client_t *tcp_client_create(const char *host, int port,
                                struct event_base *base) {
  if (!host || port <= 0) {
    return NULL;
  }

  tcp_client_t *client = calloc(1, sizeof(tcp_client_t));
  if (!client) {
    return NULL;
  }

  // Copy host address
  client->host = strdup(host);
  if (!client->host) {
    free(client);
    return NULL;
  }

  client->port = port;
  client->connected = 0;
  client->auto_reconnect = 0;
  client->reconnect_interval = 5; // Default 5-second reconnect interval
  client->conn_result = 0;        // Initially disconnected

  // Initialize synchronization primitives
  if (pthread_mutex_init(&client->conn_mutex, NULL) != 0) {
    free(client->host);
    free(client);
    return NULL;
  }

  if (pthread_cond_init(&client->conn_cond, NULL) != 0) {
    pthread_mutex_destroy(&client->conn_mutex);
    free(client->host);
    free(client);
    return NULL;
  }

  // Use the provided event loop or create a new one
  if (base) {
    client->base = base;
  } else {
    client->base = event_base_new();
    if (!client->base) {
      pthread_cond_destroy(&client->conn_cond);
      pthread_mutex_destroy(&client->conn_mutex);
      free(client->host);
      free(client);
      return NULL;
    }
  }

  return client;
}

void tcp_client_set_response_callback(tcp_client_t *client,
                                      response_callback_t callback,
                                      void *user_data) {
  if (!client)
    return;
  client->response_cb = callback;
  client->user_data = user_data;
}

void tcp_client_set_connection_callback(tcp_client_t *client,
                                        connection_callback_t callback,
                                        void *user_data) {
  if (!client)
    return;
  client->conn_cb = callback;
  client->user_data = user_data;
}

void tcp_client_set_auto_reconnect(tcp_client_t *client, int enable,
                                   int interval) {
  if (!client)
    return;
  client->auto_reconnect = enable;
  if (interval > 0) {
    client->reconnect_interval = interval;
  }
}

static int tcp_client_connect_internal(tcp_client_t *client) {
  if (!client || !client->host) {
    return -1;
  }

  // Lock mutex to update connection status
  pthread_mutex_lock(&client->conn_mutex);

  // Reset connection status
  client->connected = 0;
  client->conn_result = 0;

  // If already connected, disconnect first
  if (client->bev) {
    bufferevent_free(client->bev);
    client->bev = NULL;
  }

  pthread_mutex_unlock(&client->conn_mutex);

  // Create bufferevent
  client->bev = bufferevent_socket_new(client->base, -1, BEV_OPT_CLOSE_ON_FREE);
  if (!client->bev) {
    fprintf(stderr, "Failed to create bufferevent\n");
    pthread_mutex_lock(&client->conn_mutex);
    client->conn_result = -1;
    pthread_cond_broadcast(&client->conn_cond);
    pthread_mutex_unlock(&client->conn_mutex);
    return -1;
  }

  // Set callback functions
  bufferevent_setcb(client->bev, on_read_cb, NULL, on_event_cb, client);
  bufferevent_enable(client->bev, EV_READ | EV_WRITE);

  // Connect to server
  struct sockaddr_in sin;
  memset(&sin, 0, sizeof(sin));
  sin.sin_family = AF_INET;
  sin.sin_port = htons(client->port);

  if (inet_pton(AF_INET, client->host, &sin.sin_addr) <= 0) {
    fprintf(stderr, "Invalid IP address: %s\n", client->host);
    bufferevent_free(client->bev);
    client->bev = NULL;
    pthread_mutex_lock(&client->conn_mutex);
    client->conn_result = -1;
    pthread_cond_broadcast(&client->conn_cond);
    pthread_mutex_unlock(&client->conn_mutex);
    return -1;
  }

  if (bufferevent_socket_connect(client->bev, (struct sockaddr *)&sin,
                                 sizeof(sin)) < 0) {
    fprintf(stderr, "Failed to connect to %s:%d\n", client->host, client->port);
    bufferevent_free(client->bev);
    client->bev = NULL;
    pthread_mutex_lock(&client->conn_mutex);
    client->conn_result = -1;
    pthread_cond_broadcast(&client->conn_cond);
    pthread_mutex_unlock(&client->conn_mutex);
    return -1;
  }

  return 0;
}

int tcp_client_connect(tcp_client_t *client) {
  if (!client || !client->host) {
    return -1;
  }

  // First, attempt the internal connection
  if (tcp_client_connect_internal(client) != 0) {
    return -1;
  }

  // Start the event loop in a separate thread (only for first connection)
  pthread_t event_thread;
  if (pthread_create(&event_thread, NULL, event_loop_thread, client->base) !=
      0) {
    fprintf(stderr, "Failed to create event thread\n");
    event_base_free(client->base);
    client->base = NULL;
    pthread_mutex_lock(&client->conn_mutex);
    client->conn_result = -1;
    pthread_cond_broadcast(&client->conn_cond);
    pthread_mutex_unlock(&client->conn_mutex);
    return -1;
  }

  // Detach the thread so it cleans up automatically
  pthread_detach(event_thread);

  return 0;
}

int tcp_client_send(tcp_client_t *client, const uint8_t *data, size_t len,
                    int timeout_ms) {
  if (!client || !data || len == 0) {
    return -1;
  }

  // Lock mutex to check/wait for connection
  pthread_mutex_lock(&client->conn_mutex);

  // If not connected, wait for connection with timeout
  if (!client->connected) {
    if (timeout_ms <= 0) {
      // No timeout, wait indefinitely
      while (!client->connected && client->conn_result != -1) {
        pthread_cond_wait(&client->conn_cond, &client->conn_mutex);
      }
    } else {
      // Wait with timeout
      struct timespec ts;
      clock_gettime(CLOCK_REALTIME, &ts);

      // Add timeout to current time
      ts.tv_sec += timeout_ms / 1000;
      ts.tv_nsec += (timeout_ms % 1000) * 1000000;
      if (ts.tv_nsec >= 1000000000) {
        ts.tv_sec++;
        ts.tv_nsec -= 1000000000;
      }

      int wait_result = 0;
      while (!client->connected && client->conn_result != -1 &&
             wait_result == 0) {
        wait_result = pthread_cond_timedwait(&client->conn_cond,
                                             &client->conn_mutex, &ts);
      }

      if (wait_result == ETIMEDOUT) {
        pthread_mutex_unlock(&client->conn_mutex);
        return -2; // Timeout
      }
    }
  }

  // Check if connection failed
  if (client->conn_result == -1 || !client->connected || !client->bev) {
    pthread_mutex_unlock(&client->conn_mutex);
    return -1;
  }

  // Connection is established, send data
  int result = 0;
  if (bufferevent_write(client->bev, data, len) != 0) {
    fprintf(stderr, "Failed to send data\n");
    result = -1;
  }

  pthread_mutex_unlock(&client->conn_mutex);
  return result;
}

void tcp_client_disconnect(tcp_client_t *client) {
  if (!client)
    return;

  pthread_mutex_lock(&client->conn_mutex);

  client->connected = 0;
  client->conn_result = 0;

  if (client->bev) {
    bufferevent_free(client->bev);
    client->bev = NULL;
  }

  // Cancel reconnect timer
  if (client->reconnect_timer) {
    event_del(client->reconnect_timer);
    event_free(client->reconnect_timer);
    client->reconnect_timer = NULL;
  }

  // Notify waiting threads
  pthread_cond_broadcast(&client->conn_cond);
  pthread_mutex_unlock(&client->conn_mutex);
}

void tcp_client_destroy(tcp_client_t *client) {
  if (!client)
    return;

  tcp_client_disconnect(client);

  // Destroy synchronization primitives
  pthread_cond_destroy(&client->conn_cond);
  pthread_mutex_destroy(&client->conn_mutex);

  if (client->host) {
    free(client->host);
  }

  free(client);
}

int tcp_client_is_connected(tcp_client_t *client) {
  return client ? client->connected : 0;
}

void tcp_client_stop(tcp_client_t *client) {
  if (!client || !client->base)
    return;

  event_base_loopbreak(client->base);
}

// Internal callback function implementations
static void on_read_cb(struct bufferevent *bev, void *ctx) {
  tcp_client_t *client = (tcp_client_t *)ctx;
  struct evbuffer *input = bufferevent_get_input(bev);
  size_t len = evbuffer_get_length(input);

  if (len > 0) {
    uint8_t *data = malloc(len);
    if (data) {
      evbuffer_remove(input, data, len);

      // Call user-defined response handling callback
      if (client->response_cb) {
        client->response_cb(client, data, len, client->user_data);
      }

      free(data);
    }
  }
}

static void on_event_cb(struct bufferevent *bev, short what, void *ctx) {
  tcp_client_t *client = (tcp_client_t *)ctx;
  (void)bev; // Suppress unused parameter warning

  pthread_mutex_lock(&client->conn_mutex);

  if (what & BEV_EVENT_CONNECTED) {
    // Connection successful
    client->connected = 1;
    client->conn_result = 1;
    printf("Connected to %s:%d\n", client->host, client->port);

    // Cancel reconnect timer
    if (client->reconnect_timer) {
      event_del(client->reconnect_timer);
      event_free(client->reconnect_timer);
      client->reconnect_timer = NULL;
    }

    // Notify waiting threads
    pthread_cond_broadcast(&client->conn_cond);
    pthread_mutex_unlock(&client->conn_mutex);

    // Call connection status callback
    if (client->conn_cb) {
      client->conn_cb(client, 1, client->user_data);
    }
  } else if (what & (BEV_EVENT_ERROR | BEV_EVENT_EOF)) {
    // Connection error or disconnected
    if (what & BEV_EVENT_ERROR) {
      printf("Connection error to %s:%d: %s\n", client->host, client->port,
             evutil_socket_error_to_string(EVUTIL_SOCKET_ERROR()));
      client->conn_result = -1; // Error
    } else {
      printf("Connection closed to %s:%d\n", client->host, client->port);
      client->conn_result = 0; // Disconnected
    }

    client->connected = 0;

    // Notify waiting threads
    pthread_cond_broadcast(&client->conn_cond);
    pthread_mutex_unlock(&client->conn_mutex);

    // Call connection status callback
    if (client->conn_cb) {
      client->conn_cb(client, 0, client->user_data);
    }

    // Free bufferevent
    if (client->bev) {
      bufferevent_free(client->bev);
      client->bev = NULL;
    }

    // If auto-reconnect is enabled, schedule reconnection
    if (client->auto_reconnect) {
      schedule_reconnect(client);
    }
  }
}

static void reconnect_timer_cb(evutil_socket_t fd, short what, void *ctx) {
  tcp_client_t *client = (tcp_client_t *)ctx;
  (void)fd;   // Suppress unused parameter warning
  (void)what; // Suppress unused parameter warning

  printf("Attempting to reconnect to %s:%d...\n", client->host, client->port);

  // Clean up timer
  if (client->reconnect_timer) {
    event_free(client->reconnect_timer);
    client->reconnect_timer = NULL;
  }

  // Attempt to reconnect using internal function (no need to start event loop again)
  if (tcp_client_connect_internal(client) != 0) {
    // Connection failed, schedule reconnect again
    if (client->auto_reconnect) {
      schedule_reconnect(client);
    }
  }
}

static void schedule_reconnect(tcp_client_t *client) {
  if (!client || !client->base || client->reconnect_timer) {
    return;
  }

  struct timeval tv;
  tv.tv_sec = client->reconnect_interval;
  tv.tv_usec = 0;

  client->reconnect_timer =
      event_new(client->base, -1, 0, reconnect_timer_cb, client);
  if (client->reconnect_timer) {
    event_add(client->reconnect_timer, &tv);
    printf("Scheduled reconnect in %d seconds\n", client->reconnect_interval);
  }
}

// Thread wrapper for event_base_dispatch
static void *event_loop_thread(void *arg) {
  struct event_base *base = (struct event_base *)arg;
  event_base_dispatch(base);
  return NULL;
}