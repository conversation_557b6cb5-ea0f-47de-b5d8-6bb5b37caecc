#include "oake.h"
#include "config_loader.h"
#include "device_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Global hash table for storing OAKE data by IP
oake_data_entry_t *g_oake_data_by_ip = NULL;

static int _oake_get_and_set_device_info(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "_oake_get_and_set_device_info: data pointer is NULL\n");
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr,
            "_oake_get_and_set_device_info: Failed to get device handle\n");
    return -1;
  }

  unsigned int temp_device_id_len = OAKE_MAX_ID_LEN;
  int dev_id_rv = device_get_id(data->device_id, (int *)&temp_device_id_len);
  if (dev_id_rv != CSR_OK) {
    fprintf(
        stderr,
        "_oake_get_and_set_device_info: Failed to get device_id, error: 0x%X\n",
        dev_id_rv);
    return dev_id_rv;
  }
  data->device_id_len = temp_device_id_len;

  const unsigned char *enc_cert_ptr = NULL;
  int enc_cert_len_int = 0;
  int cert_rv = get_enc_cert_data(&enc_cert_ptr, &enc_cert_len_int);

  if (cert_rv != CSR_OK) {
    fprintf(stderr,
            "_oake_get_and_set_device_info: Failed to get encryption "
            "certificate, error: 0x%X\n",
            cert_rv);
    return cert_rv;
  }

  if (enc_cert_ptr == NULL || enc_cert_len_int <= 0) {
    fprintf(stderr, "_oake_get_and_set_device_info: Encryption certificate "
                    "data is invalid (NULL or zero/negative length).\n");
    return -1;
  }

  if ((unsigned int)enc_cert_len_int > OAKE_MAX_CERT_LEN) {
    fprintf(stderr,
            "_oake_get_and_set_device_info: Encryption certificate length (%d) "
            "exceeds buffer capacity (%d).\n",
            enc_cert_len_int, OAKE_MAX_CERT_LEN);
    return -1;
  }

  memcpy(data->cert, enc_cert_ptr, enc_cert_len_int);
  data->cert_len = (uint32_t)enc_cert_len_int;

  return CSR_OK;
}

static int _oake_parse_peer_public_key(const unsigned char *cert_data,
                                       unsigned int cert_len,
                                       unsigned char *peer_pk,
                                       unsigned int *peer_pk_len,
                                       const char *function_name) {
  if (cert_data == NULL || peer_pk == NULL || peer_pk_len == NULL) {
    fprintf(stderr, "%s: Invalid input parameters for public key parsing\n",
            function_name ? function_name : "unknown");
    return -1;
  }

  if (cert_len == 0 || cert_len > OAKE_MAX_CERT_LEN) {
    fprintf(
        stderr,
        "%s: Invalid peer certificate length (%u) for parsing public key.\n",
        function_name ? function_name : "unknown", cert_len);
    return -1;
  }

  int parse_rv =
      device_cert_parse_public_key(cert_data, cert_len, peer_pk, peer_pk_len);

  if (parse_rv != CSR_OK) {
    fprintf(
        stderr,
        "%s: Failed to parse public key from peer's certificate, error: 0x%X\n",
        function_name ? function_name : "unknown", parse_rv);
    return parse_rv;
  }

  if (*peer_pk_len == 0) {
    fprintf(stderr, "%s: Parsed public key length is zero.\n",
            function_name ? function_name : "unknown");
    return -1;
  }

  return CSR_OK;
}

static int _oake_release_key_if_not_null(void *device, void **key_handle_ptr,
                                         const char *key_name) {
  if (key_handle_ptr == NULL || *key_handle_ptr == NULL) {
    fprintf(stdout, "oake_release_resources: %s is already NULL.\n", key_name);
    return CSR_OK;
  }

  int rv = CPCI_ReleaseKey(device, *key_handle_ptr, 0);
  if (rv != CSR_OK) {
    fprintf(stderr,
            "oake_release_resources: Failed to release %s, error: 0x%X\n",
            key_name, rv);
  } else {
    fprintf(stdout, "oake_release_resources: Successfully released %s.\n",
            key_name);
  }
  *key_handle_ptr = NULL;
  return rv;
}

int oake_get_device_id(unsigned char *device_id_buf, uint32_t *device_id_len) {
  if (device_id_buf == NULL || device_id_len == NULL) {
    fprintf(stderr, "oake_get_device_id: Invalid input parameters\n");
    return -1;
  }

  if (*device_id_len == 0 || *device_id_len > OAKE_MAX_ID_LEN) {
    fprintf(stderr,
            "oake_get_device_id: Invalid device_id buffer length (%u)\n",
            *device_id_len);
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_get_device_id: Failed to get device handle\n");
    return -1;
  }

  unsigned int temp_device_id_len = *device_id_len;
  int dev_id_rv = device_get_id(device_id_buf, (int *)&temp_device_id_len);
  if (dev_id_rv != CSR_OK) {
    fprintf(stderr,
            "oake_get_device_id: Failed to get device_id, error: 0x%X\n",
            dev_id_rv);
    return dev_id_rv;
  }

  *device_id_len = temp_device_id_len;
  return CSR_OK;
}

int oake_init(const unsigned char *device_id, unsigned int device_id_len,
              oake_generic_data_t **data_out) {
  if (data_out == NULL) {
    fprintf(stderr, "oake_init: output parameter data_out is NULL\n");
    return -1;
  }
  int allocated_internally = 0;
  if (device_id == NULL) {
    fprintf(stderr, "oake_init: device_id pointer is NULL\n");
    return -1;
  }
  if (device_id_len == 0 || device_id_len > OAKE_MAX_ID_LEN) {
    fprintf(stderr, "oake_init: invalid device_id_len %u\n", device_id_len);
    return -1;
  }

  if (*data_out == NULL) {
    *data_out = (oake_generic_data_t *)malloc(sizeof(oake_generic_data_t));
    if (*data_out == NULL) {
      fprintf(stderr,
              "oake_init: Failed to allocate memory for oake_generic_data_t\n");
      return -1; // *data_out is still NULL, no memory to free here.
    }
    allocated_internally = 1;
  }
  memset(*data_out, 0, sizeof(oake_generic_data_t));

  int rv;
  unsigned char internal_out_data[4096];

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_init: Failed to get device handle\n");
    if (allocated_internally && *data_out != NULL) {
      free(*data_out);
      *data_out = NULL; // Set to NULL as we allocated and are now freeing.
    }
    // If not allocated internally, *data_out (if non-NULL) is managed by the
    // caller.
    return -1;
  }

  memcpy((*data_out)->device_id, device_id, device_id_len);
  (*data_out)->device_id_len = device_id_len;

  const task_config_t *config = get_global_config();
  if (config == NULL) {
    fprintf(stderr, "oake_init: Failed to get global config\n");
    if (allocated_internally && *data_out != NULL) {
      free(*data_out);
      *data_out = NULL;
    }
    return -1;
  }

  int symm_alg_no = config->csl.symm_algo;
  int asymm_alg_no = config->csl.asymm_algo;
  int hash_alg_no = 0;
  int type = 0;
  int server_flag = 0;
  int flag = 0;

  rv = CPCI_OAKE_ECC_Init(device, symm_alg_no, hash_alg_no, asymm_alg_no,
                          (*data_out)->device_id, (*data_out)->device_id_len,
                          type, server_flag, &(*data_out)->ctx,
                          internal_out_data, flag);

  if (rv != CSR_OK) {
    fprintf(stderr, "oake_init: CPCI_OAKE_ECC_Init failed, error: 0x%X\n", rv);
    // Clean up allocated memory on failure only if allocated internally
    if (allocated_internally && *data_out != NULL) {
      free(*data_out);
      *data_out = NULL;
    }
  }

  return rv;
}

int oake_init_with_ctx(const unsigned char *device_id,
                       unsigned int device_id_len, void *ctx) {
  if (device_id == NULL) {
    fprintf(stderr, "oake_init_with_ctx: device_id pointer is NULL\n");
    return -1;
  }
  if (device_id_len == 0 || device_id_len > OAKE_MAX_ID_LEN) {
    fprintf(stderr, "oake_init_with_ctx: invalid device_id_len %u\n",
            device_id_len);
    return -1;
  }
  if (ctx == NULL) {
    fprintf(stderr, "oake_init_with_ctx: ctx pointer is NULL\n");
    return -1;
  }

  int rv;
  unsigned char internal_out_data[4096];

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_init_with_ctx: Failed to get device handle\n");
    return -1;
  }

  const task_config_t *config = get_global_config();
  if (config == NULL) {
    fprintf(stderr, "oake_init_with_ctx: Failed to get global config\n");
    return -1;
  }

  int symm_alg_no = config->csl.symm_algo;
  int asymm_alg_no = config->csl.asymm_algo;
  int hash_alg_no = 0;
  int type = 0;
  int server_flag = 0;
  int flag = 0;

  // Use the provided ctx directly
  rv = CPCI_OAKE_ECC_Init(device, symm_alg_no, hash_alg_no, asymm_alg_no,
                          (unsigned char *)device_id, device_id_len, type,
                          server_flag, &ctx, internal_out_data, flag);

  if (rv != CSR_OK) {
    fprintf(stderr,
            "oake_init_with_ctx: CPCI_OAKE_ECC_Init failed, error: 0x%X\n", rv);
  }

  return rv;
}

int oake_create_request(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "oake_create_request: data pointer is NULL\n");
    return -1;
  }

  if (data->ctx == NULL) {
    fprintf(
        stderr,
        "oake_create_request: OAKE context is NULL. Call oake_init first.\n");
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_create_request: Failed to get device handle\n");
    return -1;
  }

  int rv = CPCI_OAKE_ECC_A1(device, data->ctx, (char *)data->rnd,
                            (int *)&(data->rnd_len), 0);

  if (rv != CSR_OK) {
    fprintf(stderr,
            "oake_create_request: CPCI_OAKE_ECC_A1 failed, error: 0x%X\n", rv);
    return rv;
  }

  int info_rv = _oake_get_and_set_device_info(data);
  if (info_rv != CSR_OK) {
    return info_rv;
  }

  return rv;
}

int oake_create_response(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "oake_create_response: data pointer is NULL\n");
    return -1;
  }

  if (data->ctx == NULL) {
    fprintf(
        stderr,
        "oake_create_response: OAKE context is NULL. Call oake_init first.\n");
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_create_response: Failed to get device handle\n");
    return -1;
  }

  unsigned char peer_pk[OAKE_MAX_CERT_LEN];
  unsigned int peer_pk_len = sizeof(peer_pk);

  int parse_rv =
      _oake_parse_peer_public_key(data->cert, data->cert_len, peer_pk,
                                  &peer_pk_len, "oake_create_response");

  if (parse_rv != CSR_OK) {
    return parse_rv;
  }

  unsigned char output_rb[OAKE_MAX_RND_LEN];
  unsigned int output_rb_len = 0;

  int rv = CPCI_OAKE_ECC_B1(
      device, data->ctx, (char *)data->rnd, (int)data->rnd_len, (char *)peer_pk,
      (int)peer_pk_len, (char *)output_rb, (int *)&output_rb_len,
      (char *)data->mac, (int *)&(data->mac_len), 0);

  if (rv != CSR_OK) {
    fprintf(stderr,
            "oake_create_response: CPCI_OAKE_ECC_B1 failed, error: 0x%X\n", rv);
    return rv;
  }

  if (output_rb_len > OAKE_MAX_RND_LEN) {
    fprintf(stderr,
            "oake_create_response: Output RbLen (%u) exceeds buffer capacity "
            "(%d).\n",
            output_rb_len, OAKE_MAX_RND_LEN);
    return -1;
  }
  memcpy(data->rnd, output_rb, output_rb_len);
  data->rnd_len = output_rb_len;

  // This will overwrite data->cert with the local device's certificate.
  int info_rv = _oake_get_and_set_device_info(data);
  if (info_rv != CSR_OK) {
    return info_rv;
  }

  return rv;
}

int oake_create_acknowledgement(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "oake_create_acknowledgement: data pointer is NULL\n");
    return -1;
  }

  if (data->ctx == NULL) {
    fprintf(stderr, "oake_create_acknowledgement: OAKE context is NULL. Call "
                    "oake_init first.\n");
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr,
            "oake_create_acknowledgement: Failed to get device handle\n");
    return -1;
  }

  unsigned char peer_pk[OAKE_MAX_CERT_LEN];
  unsigned int peer_pk_len = sizeof(peer_pk);

  int parse_rv =
      _oake_parse_peer_public_key(data->cert, data->cert_len, peer_pk,
                                  &peer_pk_len, "oake_create_acknowledgement");

  if (parse_rv != CSR_OK) {
    return parse_rv;
  }

  unsigned char output_maca[OAKE_MAX_MAC_LEN];
  unsigned int output_maca_len = 0;

  int rv = CPCI_OAKE_ECC_A2(
      device, data->ctx, (char *)data->rnd, (int)data->rnd_len, (char *)peer_pk,
      (int)peer_pk_len, (char *)data->mac, (int)data->mac_len,
      (char *)output_maca, (int *)&output_maca_len, &(data->remote_key),
      &(data->local_key), 0);

  if (rv != CSR_OK) {
    fprintf(
        stderr,
        "oake_create_acknowledgement: CPCI_OAKE_ECC_A2 failed, error: 0x%X\n",
        rv);
    return rv;
  }

  if (output_maca_len > OAKE_MAX_MAC_LEN) {
    fprintf(stderr,
            "oake_create_acknowledgement: Output MACALen (%u) exceeds buffer "
            "capacity (%d).\n",
            output_maca_len, OAKE_MAX_MAC_LEN);
    return -1;
  }
  memcpy(data->mac, output_maca, output_maca_len);
  data->mac_len = output_maca_len;

  return CSR_OK;
}

int oake_process_acknowledgement(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "oake_process_acknowledgement: data pointer is NULL\n");
    return -1;
  }

  if (data->ctx == NULL) {
    fprintf(stderr, "oake_process_acknowledgement: OAKE context is NULL. Call "
                    "oake_init first.\n");
    return -1;
  }

  if (data->mac_len == 0) {
    fprintf(stderr,
            "oake_process_acknowledgement: MACA data is missing or invalid.\n");
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr,
            "oake_process_acknowledgement: Failed to get device handle\n");
    return -1;
  }

  int rv =
      CPCI_OAKE_ECC_B2(device, data->ctx, (char *)data->mac, (int)data->mac_len,
                       &(data->remote_key), &(data->local_key), 0);

  if (rv != CSR_OK) {
    fprintf(
        stderr,
        "oake_process_acknowledgement: CPCI_OAKE_ECC_B2 failed, error: 0x%X\n",
        rv);
    return rv;
  }

  return CSR_OK;
}

int oake_finalize_session(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "oake_finalize_session: data pointer is NULL\n");
    return -1;
  }

  if (data->ctx == NULL) {
    // Context might already be finalized or was never initialized.
    // Depending on desired behavior, this could be a no-op or an error.
    // For now, treating as a no-op if ctx is NULL, assuming it's already
    // handled.
    fprintf(stdout, "oake_finalize_session: OAKE context is already NULL. No "
                    "action taken.\n");
    return CSR_OK;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_finalize_session: Failed to get device handle\n");
    return -1;
  }

  int rv = CPCI_OAKE_ECC_Final(device, data->ctx, 0);

  if (rv != CSR_OK) {
    fprintf(stderr,
            "oake_finalize_session: CPCI_OAKE_ECC_Final failed, error: 0x%X\n",
            rv);
    data->ctx = NULL;
    return rv;
  }

  data->ctx = NULL;

  return CSR_OK;
}

int oake_release_resources(oake_generic_data_t *data) {
  if (data == NULL) {
    fprintf(stderr, "oake_release_resources: data pointer is NULL\n");
    return -1;
  }

  void *device = device_get_api_handle();
  if (device == NULL) {
    fprintf(stderr, "oake_release_resources: Failed to get device handle\n");
    return -1;
  }

  int final_rv = CSR_OK;
  int rv;

  rv = _oake_release_key_if_not_null(device, &data->remote_key, "remote_key");
  if (rv != CSR_OK) {
    final_rv = rv;
  }

  rv = _oake_release_key_if_not_null(device, &data->local_key, "local_key");
  if (rv != CSR_OK && final_rv == CSR_OK) {
    final_rv = rv;
  }

  return final_rv;
}

// Note: ctx, remote_key, and local_key are excluded from serialization
size_t oake_get_serialized_size(const oake_generic_data_t *data) {
  if (data == NULL) {
    return 0;
  }

  // Add space for src_ip and dst_ip strings (including null terminators)
  size_t src_ip_len = strlen(data->src_ip) + 1;
  size_t dst_ip_len = strlen(data->dst_ip) + 1;

  size_t total_size = sizeof(uint32_t) * 6 + data->device_id_len +
                      data->cert_len + data->rnd_len + data->mac_len +
                      src_ip_len + dst_ip_len;

  return total_size;
}

// Returns the number of bytes written, or -1 on error
int oake_serialize(const oake_generic_data_t *data, uint8_t *buffer,
                   size_t buffer_size) {
  if (data == NULL || buffer == NULL) {
    fprintf(stderr, "oake_serialize: Invalid input parameters\n");
    return -1;
  }

  size_t required_size = oake_get_serialized_size(data);
  if (buffer_size < required_size) {
    fprintf(stderr,
            "oake_serialize: Buffer too small. Required: %zu, Available: %zu\n",
            required_size, buffer_size);
    return -1;
  }

  uint8_t *ptr = buffer;

  memcpy(ptr, &data->device_id_len, sizeof(uint32_t));
  ptr += sizeof(uint32_t);
  if (data->device_id_len > 0) {
    memcpy(ptr, data->device_id, data->device_id_len);
    ptr += data->device_id_len;
  }

  memcpy(ptr, &data->cert_len, sizeof(uint32_t));
  ptr += sizeof(uint32_t);
  if (data->cert_len > 0) {
    memcpy(ptr, data->cert, data->cert_len);
    ptr += data->cert_len;
  }

  memcpy(ptr, &data->rnd_len, sizeof(uint32_t));
  ptr += sizeof(uint32_t);
  if (data->rnd_len > 0) {
    memcpy(ptr, data->rnd, data->rnd_len);
    ptr += data->rnd_len;
  }

  memcpy(ptr, &data->mac_len, sizeof(uint32_t));
  ptr += sizeof(uint32_t);
  if (data->mac_len > 0) {
    memcpy(ptr, data->mac, data->mac_len);
    ptr += data->mac_len;
  }

  // Serialize src_ip
  uint32_t src_ip_len = strlen(data->src_ip) + 1;
  memcpy(ptr, &src_ip_len, sizeof(uint32_t));
  ptr += sizeof(uint32_t);
  if (src_ip_len > 0) {
    memcpy(ptr, data->src_ip, src_ip_len);
    ptr += src_ip_len;
  }

  // Serialize dst_ip
  uint32_t dst_ip_len = strlen(data->dst_ip) + 1;
  memcpy(ptr, &dst_ip_len, sizeof(uint32_t));
  ptr += sizeof(uint32_t);
  if (dst_ip_len > 0) {
    memcpy(ptr, data->dst_ip, dst_ip_len);
    ptr += dst_ip_len;
  }

  return (int)(ptr - buffer);
}

// Returns 0 on success, -1 on error
int oake_deserialize(const uint8_t *buffer, size_t buffer_size,
                     oake_generic_data_t *data) {
  if (buffer == NULL || data == NULL) {
    fprintf(stderr, "oake_deserialize: Invalid input parameters\n");
    return -1;
  }

  const uint8_t *ptr = buffer;
  const uint8_t *end = buffer + buffer_size;

  void *saved_ctx = data->ctx;
  void *saved_remote_key = data->remote_key;
  void *saved_local_key = data->local_key;

  memset(data, 0, sizeof(oake_generic_data_t));

  data->ctx = saved_ctx;
  data->remote_key = saved_remote_key;
  data->local_key = saved_local_key;

  if (ptr + sizeof(uint32_t) > end) {
    fprintf(stderr, "oake_deserialize: Buffer too small for device_id_len\n");
    return -1;
  }
  memcpy(&data->device_id_len, ptr, sizeof(uint32_t));
  ptr += sizeof(uint32_t);

  if (data->device_id_len > OAKE_MAX_ID_LEN) {
    fprintf(stderr,
            "oake_deserialize: device_id_len (%u) exceeds maximum (%d)\n",
            data->device_id_len, OAKE_MAX_ID_LEN);
    return -1;
  }

  if (data->device_id_len > 0) {
    if (ptr + data->device_id_len > end) {
      fprintf(stderr,
              "oake_deserialize: Buffer too small for device_id data\n");
      return -1;
    }
    memcpy(data->device_id, ptr, data->device_id_len);
    ptr += data->device_id_len;
  }

  if (ptr + sizeof(uint32_t) > end) {
    fprintf(stderr, "oake_deserialize: Buffer too small for cert_len\n");
    return -1;
  }
  memcpy(&data->cert_len, ptr, sizeof(uint32_t));
  ptr += sizeof(uint32_t);

  if (data->cert_len > OAKE_MAX_CERT_LEN) {
    fprintf(stderr, "oake_deserialize: cert_len (%u) exceeds maximum (%d)\n",
            data->cert_len, OAKE_MAX_CERT_LEN);
    return -1;
  }

  if (data->cert_len > 0) {
    if (ptr + data->cert_len > end) {
      fprintf(stderr, "oake_deserialize: Buffer too small for cert data\n");
      return -1;
    }
    memcpy(data->cert, ptr, data->cert_len);
    ptr += data->cert_len;
  }

  if (ptr + sizeof(uint32_t) > end) {
    fprintf(stderr, "oake_deserialize: Buffer too small for rnd_len\n");
    return -1;
  }
  memcpy(&data->rnd_len, ptr, sizeof(uint32_t));
  ptr += sizeof(uint32_t);

  if (data->rnd_len > OAKE_MAX_RND_LEN) {
    fprintf(stderr, "oake_deserialize: rnd_len (%u) exceeds maximum (%d)\n",
            data->rnd_len, OAKE_MAX_RND_LEN);
    return -1;
  }

  if (data->rnd_len > 0) {
    if (ptr + data->rnd_len > end) {
      fprintf(stderr, "oake_deserialize: Buffer too small for rnd data\n");
      return -1;
    }
    memcpy(data->rnd, ptr, data->rnd_len);
    ptr += data->rnd_len;
  }

  if (ptr + sizeof(uint32_t) > end) {
    fprintf(stderr, "oake_deserialize: Buffer too small for mac_len\n");
    return -1;
  }
  memcpy(&data->mac_len, ptr, sizeof(uint32_t));
  ptr += sizeof(uint32_t);

  if (data->mac_len > OAKE_MAX_MAC_LEN) {
    fprintf(stderr, "oake_deserialize: mac_len (%u) exceeds maximum (%d)\n",
            data->mac_len, OAKE_MAX_MAC_LEN);
    return -1;
  }

  if (data->mac_len > 0) {
    if (ptr + data->mac_len > end) {
      fprintf(stderr, "oake_deserialize: Buffer too small for mac data\n");
      return -1;
    }
    memcpy(data->mac, ptr, data->mac_len);
    ptr += data->mac_len;
  }

  // Deserialize src_ip
  if (ptr + sizeof(uint32_t) > end) {
    fprintf(stderr, "oake_deserialize: Buffer too small for src_ip_len\n");
    return -1;
  }
  uint32_t src_ip_len;
  memcpy(&src_ip_len, ptr, sizeof(uint32_t));
  ptr += sizeof(uint32_t);

  if (src_ip_len > OAKE_MAX_IP_LEN) {
    fprintf(stderr, "oake_deserialize: src_ip_len (%u) exceeds maximum (%d)\n",
            src_ip_len, OAKE_MAX_IP_LEN);
    return -1;
  }

  if (src_ip_len > 0) {
    if (ptr + src_ip_len > end) {
      fprintf(stderr, "oake_deserialize: Buffer too small for src_ip data\n");
      return -1;
    }
    memcpy(data->src_ip, ptr, src_ip_len);
    ptr += src_ip_len;
    // Ensure null termination
    data->src_ip[src_ip_len - 1] = '\0';
  }

  // Deserialize dst_ip
  if (ptr + sizeof(uint32_t) > end) {
    fprintf(stderr, "oake_deserialize: Buffer too small for dst_ip_len\n");
    return -1;
  }
  uint32_t dst_ip_len;
  memcpy(&dst_ip_len, ptr, sizeof(uint32_t));
  ptr += sizeof(uint32_t);

  if (dst_ip_len > OAKE_MAX_IP_LEN) {
    fprintf(stderr, "oake_deserialize: dst_ip_len (%u) exceeds maximum (%d)\n",
            dst_ip_len, OAKE_MAX_IP_LEN);
    return -1;
  }

  if (dst_ip_len > 0) {
    if (ptr + dst_ip_len > end) {
      fprintf(stderr, "oake_deserialize: Buffer too small for dst_ip data\n");
      return -1;
    }
    memcpy(data->dst_ip, ptr, dst_ip_len);
    ptr += dst_ip_len;
    // Ensure null termination
    data->dst_ip[dst_ip_len - 1] = '\0';
  }

  return 0;
}

// Hash table management functions implementation

// Function to get OAKE data by IP address
oake_generic_data_t *oake_get_data_by_ip(const char *ip) {
  if (ip == NULL) {
    fprintf(stderr, "oake_get_data_by_ip: IP address is NULL\n");
    return NULL;
  }

  if (strlen(ip) >= OAKE_MAX_IP_LEN) {
    fprintf(stderr, "oake_get_data_by_ip: IP address too long\n");
    return NULL;
  }

  oake_data_entry_t *entry = NULL;
  HASH_FIND_STR(g_oake_data_by_ip, ip, entry);

  if (entry != NULL) {
    return &(entry->data);
  }

  return NULL;
}

// Function to add/update OAKE data by IP address
int oake_set_data_by_ip(const char *ip, const oake_generic_data_t *data) {
  if (ip == NULL || data == NULL) {
    fprintf(stderr, "oake_set_data_by_ip: Invalid input parameters\n");
    return -1;
  }

  if (strlen(ip) >= OAKE_MAX_IP_LEN) {
    fprintf(stderr, "oake_set_data_by_ip: IP address too long\n");
    return -1;
  }

  oake_data_entry_t *entry = NULL;
  HASH_FIND_STR(g_oake_data_by_ip, ip, entry);

  if (entry != NULL) {
    // Update existing entry
    memcpy(&(entry->data), data, sizeof(oake_generic_data_t));
    fprintf(stdout, "oake_set_data_by_ip: Updated data for IP %s\n", ip);
  } else {
    // Create new entry
    entry = (oake_data_entry_t *)malloc(sizeof(oake_data_entry_t));
    if (entry == NULL) {
      fprintf(stderr, "oake_set_data_by_ip: Failed to allocate memory\n");
      return -1;
    }

    // Copy IP address and data
    strncpy(entry->ip, ip, OAKE_MAX_IP_LEN - 1);
    entry->ip[OAKE_MAX_IP_LEN - 1] = '\0'; // Ensure null termination
    memcpy(&(entry->data), data, sizeof(oake_generic_data_t));

    // Add to hash table
    HASH_ADD_STR(g_oake_data_by_ip, ip, entry);
    fprintf(stdout, "oake_set_data_by_ip: Added new data for IP %s\n", ip);
  }

  return 0;
}

// Function to remove OAKE data by IP address
int oake_remove_data_by_ip(const char *ip) {
  if (ip == NULL) {
    fprintf(stderr, "oake_remove_data_by_ip: IP address is NULL\n");
    return -1;
  }

  oake_data_entry_t *entry = NULL;
  HASH_FIND_STR(g_oake_data_by_ip, ip, entry);

  if (entry != NULL) {
    // Release resources before removing from hash table
    int release_rv = oake_release_resources(&(entry->data));
    if (release_rv != 0) {
      fprintf(stderr,
              "oake_remove_data_by_ip: Warning - failed to release resources "
              "for IP %s, error: %d\n",
              ip, release_rv);
    }

    HASH_DEL(g_oake_data_by_ip, entry);
    free(entry);
    fprintf(stdout, "oake_remove_data_by_ip: Removed data for IP %s\n", ip);
    return 0;
  }

  fprintf(stderr, "oake_remove_data_by_ip: No data found for IP %s\n", ip);
  return -1;
}

// Function to clear all OAKE data from hash table
void oake_clear_all_data(void) {
  oake_data_entry_t *entry, *tmp;
  int release_errors = 0;

  HASH_ITER(hh, g_oake_data_by_ip, entry, tmp) {
    // Release resources before removing from hash table
    int release_rv = oake_release_resources(&(entry->data));
    if (release_rv != 0) {
      fprintf(stderr,
              "oake_clear_all_data: Warning - failed to release resources for "
              "IP %s, error: %d\n",
              entry->ip, release_rv);
      release_errors++;
    }

    HASH_DEL(g_oake_data_by_ip, entry);
    free(entry);
  }

  g_oake_data_by_ip = NULL;

  if (release_errors > 0) {
    fprintf(stdout,
            "oake_clear_all_data: Cleared all OAKE data from hash table with "
            "%d resource release errors\n",
            release_errors);
  } else {
    fprintf(stdout, "oake_clear_all_data: Cleared all OAKE data from hash "
                    "table successfully\n");
  }
}

// Function to get the count of entries in the hash table
unsigned int oake_get_data_count(void) { return HASH_COUNT(g_oake_data_by_ip); }
