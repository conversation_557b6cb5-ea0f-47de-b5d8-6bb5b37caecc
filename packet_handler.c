#define _GNU_SOURCE // For certain networking features and definitions
#include "packet_handler.h"
#include "config_loader.h"
#include "crypto_utils.h"
#include "oake.h"
#include "packet_utils.h"
#include "raw_sender.h"
#include <arpa/inet.h> // For inet_ntop, inet_pton
#include <errno.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>

// Helper function to decrypt packet data if encryption is enabled
// Returns decrypted buffer or NULL on failure/no encryption
// Sets *out_decrypted_len to the length of the decrypted buffer
// Uses the provided src_ip to get OAKE data
static uint8_t *decrypt_packet_if_enabled(const uint8_t *buffer, size_t len,
                                          size_t *out_decrypted_len,
                                          const char *src_ip) {
  const task_config_t *config = get_global_config();
  if (config == NULL) {
    fprintf(stderr, "decrypt_packet_if_enabled: Global config not loaded.\n");
    return NULL;
  }

  if (config->csl.encrypt != 1) {
    *out_decrypted_len = 0;
    return NULL;
  }

  printf("Decryption enabled, processing packet...\n");

  printf("Extracting OAKE data for source IP: '%s'\n", src_ip);

  // Get OAKE data by IP address instead of by role
  oake_generic_data_t *current_oake_data = oake_get_data_by_ip(src_ip);
  if (current_oake_data == NULL) {
    fprintf(stderr,
            "decrypt_packet_if_enabled: Failed to get OAKE data for IP: %s\n",
            src_ip);
    return NULL;
  }

  if (current_oake_data->remote_key == NULL) {
    fprintf(stderr, "decrypt_packet_if_enabled: Remote key not available for "
                    "decryption.\n");
    return NULL;
  }

  // Minimum size check: need at least 16 bytes for MAC
  if (len < 16) {
    fprintf(stderr,
            "decrypt_packet_if_enabled: Packet too short for encrypted data "
            "(len: %zu, min: 16).\n",
            len);
    return NULL;
  }

  // Extract ciphertext and MAC from the combined buffer
  // Based on encrypt_packet_if_enabled: encrypted_data + MAC (16 bytes)
  size_t ciphertext_len = len - 16;
  const uint8_t *ciphertext_data = buffer;
  const uint8_t *mac_data = buffer + ciphertext_len;

  unsigned int plaintext_buffer_len =
      (unsigned int)ciphertext_len + 64; // Add some extra space
  uint8_t *plaintext_buffer = (uint8_t *)malloc(plaintext_buffer_len);

  if (plaintext_buffer == NULL) {
    fprintf(stderr, "decrypt_packet_if_enabled: Failed to allocate memory for "
                    "decryption buffer.\n");
    return NULL;
  }

  SM_RV decrypt_result =
      crypto_decrypt_data(current_oake_data->remote_key, ciphertext_data,
                          (unsigned int)ciphertext_len, plaintext_buffer,
                          &plaintext_buffer_len, mac_data,
                          16 // MAC length is 16 bytes
      );

  if (decrypt_result != CSR_OK) {
    fprintf(stderr,
            "decrypt_packet_if_enabled: Decryption failed with error: 0x%X\n",
            decrypt_result);
    free(plaintext_buffer);
    return NULL;
  }

  *out_decrypted_len = (size_t)plaintext_buffer_len;
  printf("Packet decrypted successfully. Encrypted+MAC size: %zu, Decrypted "
         "size: %zu\n",
         len, *out_decrypted_len);

  return plaintext_buffer;
}

// Helper function to trim specified bytes from the start and end of a data
// buffer. Modifies the data_ptr to point to the new start and len_ptr to the
// new length. Returns 0 on success, -1 on failure (e.g., packet too short).
static int trim_packet_data(uint8_t **data_ptr, size_t *len_ptr,
                            size_t bytes_to_trim_each_end) {
  if (data_ptr == NULL || *data_ptr == NULL || len_ptr == NULL) {
    fprintf(stderr, "trim_packet_data: Null pointer argument provided.\n");
    return -1;
  }

  size_t total_bytes_to_trim = 2 * bytes_to_trim_each_end;

  if (*len_ptr < total_bytes_to_trim) {
    fprintf(stderr,
            "trim_packet_data: Packet length %zu is less than total bytes to "
            "trim %zu.\n",
            *len_ptr, total_bytes_to_trim);
    return -1;
  }

  *data_ptr += bytes_to_trim_each_end;
  *len_ptr -= total_bytes_to_trim;

  return 0;
}

// Helper function to process and remove dual IP headers (src_ip and dest_ip)
// Each IP is 16 bytes, so total header size is 32 bytes
// Returns 0 on success, -1 on failure
// Modifies buffer_ptr to point past the IP headers and len_ptr to new length
// Saves the source IP string in src_ip_out if provided
static int process_and_remove_ip_headers(uint8_t **buffer_ptr, size_t *len_ptr,
                                         char *src_ip_out) {
  if (buffer_ptr == NULL || *buffer_ptr == NULL || len_ptr == NULL) {
    fprintf(stderr,
            "process_and_remove_ip_headers: Null pointer argument provided.\n");
    return -1;
  }

  const size_t SINGLE_IP_SIZE = 16;
  const size_t DUAL_IP_HEADER_SIZE = 2 * SINGLE_IP_SIZE; // 32 bytes total

  if (*len_ptr < DUAL_IP_HEADER_SIZE) {
    fprintf(stderr,
            "process_and_remove_ip_headers: Packet too short to contain dual "
            "IP headers "
            "(len: %zu, required: %zu).\n",
            *len_ptr, DUAL_IP_HEADER_SIZE);
    return -1;
  }

  uint8_t *buffer = *buffer_ptr;

  char src_ip_str[17]; // 16 bytes + null terminator
  memcpy(src_ip_str, buffer, SINGLE_IP_SIZE);
  src_ip_str[16] = '\0'; // Ensure null termination
  printf("Source IP header (16 bytes): '%s'\n", src_ip_str);

  if (src_ip_out != NULL) {
    memcpy(src_ip_out, src_ip_str, 17);
  }

  char dest_ip_str[17]; // 16 bytes + null terminator
  memcpy(dest_ip_str, buffer + SINGLE_IP_SIZE, SINGLE_IP_SIZE);
  dest_ip_str[16] = '\0'; // Ensure null termination
  printf("Destination IP header (16 bytes): '%s'\n", dest_ip_str);

  *buffer_ptr += DUAL_IP_HEADER_SIZE;
  *len_ptr -= DUAL_IP_HEADER_SIZE;

  printf("Removed dual IP headers (32 bytes). New packet length: %zu\n",
         *len_ptr);

  return 0;
}

static int validate_and_initialize_socket(void);
static int process_packet_data(uint8_t **buffer_ptr, size_t *len_ptr,
                               const task_config_t *config, char *src_ip_out);
static int send_processed_packet(uint8_t *data, size_t data_len);

static int validate_and_initialize_socket(void) {
  if (initialize_sender() != 0) {
    fprintf(stderr, "packet_handler (raw_cb): Failed to initialize sender.\n");
    return -1;
  }

  int sock_fd = get_raw_socket_fd();
  if (sock_fd == -1) {
    fprintf(
        stderr,
        "packet_handler (raw_cb): Raw socket not initialized or invalid.\n");
    return -1;
  }

  return 0;
}

// Helper function to process packet data (trim, remove headers, decrypt)
// Also saves the source IP from the headers if src_ip_out is provided
static int process_packet_data(uint8_t **buffer_ptr, size_t *len_ptr,
                               const task_config_t *config, char *src_ip_out) {
  const size_t bytes_to_trim = (size_t)config->sitp_task.pkt_trim_bytes;
  size_t original_len = *len_ptr;

  if (bytes_to_trim > 0) {
    if (trim_packet_data(buffer_ptr, len_ptr, bytes_to_trim) != 0) {
      fprintf(stderr,
              "packet_handler (raw_cb): Failed to trim packet. Original len: "
              "%zu. Attempted to trim %zu from each end.\n",
              original_len, bytes_to_trim);
      return -1;
    }
  }

  if (process_and_remove_ip_headers(buffer_ptr, len_ptr, src_ip_out) != 0) {
    fprintf(stderr, "packet_handler (raw_cb): Failed to remove IP headers.\n");
    return -1;
  }

  return 0;
}

static uint8_t *handle_decryption(uint8_t *buffer, size_t len, size_t *out_len,
                                  const task_config_t *config,
                                  const char *src_ip) {
  uint8_t *decrypted_buffer =
      decrypt_packet_if_enabled(buffer, len, out_len, src_ip);

  if (decrypted_buffer != NULL) {
    return decrypted_buffer;
  } else if (config->csl.encrypt == 1) {
    fprintf(stderr,
            "packet_handler (raw_cb): Decryption failed. Dropping packet.\n");
    return NULL;
  }

  *out_len = len;
  return buffer; // Return original buffer (not a new allocation)
}

static int send_processed_packet(uint8_t *data, size_t data_len) {
  if (data == NULL || data_len < sizeof(struct ip)) {
    fprintf(stderr,
            "packet_handler (raw_cb): Invalid packet buffer or length too "
            "short (len: %zu, min IP header len: %zu).\n",
            data_len, sizeof(struct ip));
    return -1;
  }

  struct ip *iph = (struct ip *)data;
  struct sockaddr_in dest_saddr;

  memset(&dest_saddr, 0, sizeof(dest_saddr));
  dest_saddr.sin_family = AF_INET;
  dest_saddr.sin_addr = iph->ip_dst;
  dest_saddr.sin_port = extract_tcp_dest_port(data, data_len);

  printf("--- Writing Back Packet Details ---\n");
  print_ip_and_ports(data, data_len, NULL, NULL, NULL, NULL);
  print_payload(data, data_len);
  printf("----------------------------\n");

  int sock_fd = get_raw_socket_fd();
  ssize_t bytes_sent =
      sendto(sock_fd, data, data_len, 0, (struct sockaddr *)&dest_saddr,
             sizeof(dest_saddr));

  if (bytes_sent < 0) {
    if (errno == EAGAIN || errno == EWOULDBLOCK) {
      fprintf(stderr, "packet_handler (raw_cb): sendto would block "
                      "(EAGAIN/EWOULDBLOCK).\n");
    } else {
      perror("packet_handler (raw_cb): sendto error");
    }
    return -1;
  }

  if ((size_t)bytes_sent != data_len) {
    fprintf(stderr,
            "packet_handler (raw_cb): sendto sent %zd bytes, but expected %zu "
            "bytes (partial send).\n",
            bytes_sent, data_len);
    return -1;
  }

  return 0;
}

void raw_packet_receive_callback(void *arg, uint8_t *buffer, size_t len) {
  (void)arg;

  const task_config_t *config = get_global_config();
  // config pointer should always be valid due to initialization in main
  // and fallback in get_global_config.

  if (validate_and_initialize_socket() != 0) {
    return;
  }

  char src_ip_str[17];
  if (process_packet_data(&buffer, &len, config, src_ip_str) != 0) {
    return;
  }

  size_t processed_len = 0;
  uint8_t *processed_data =
      handle_decryption(buffer, len, &processed_len, config, src_ip_str);
  if (processed_data == NULL) {
    return; // Decryption failed or encryption enabled but failed
  }

  int send_result = send_processed_packet(processed_data, processed_len);

  // Step 5: Clean up (only if we allocated new memory for decryption)
  if (processed_data != buffer) {
    free(processed_data);
  }

  if (send_result != 0) {
    return;
  }
}
