#include "device_manager.h"
#include "config_loader.h"
#include "include/bsk_api.h" // For CS_USERADMIN and other CSR_ codes
#include <stdio.h>  // For NULL, though stdlib.h or stddef.h is more common
#include <stdlib.h>
#include <string.h>

#define MAX_NAME_LEN 1024 // Max expected size for named data (e.g., certificates)
#define MAX_DEVICE_ID_LEN 64 // Max expected size for device ID

// Fixed parameters are now loaded from config
// Certificate data will also be stored here
struct device_context_s {
  void *handle;   // Stores the handle from CPCI_OpenDevice
  int login_type; // Stores the login type for logout
  unsigned char *enc_cert_data;
  int enc_cert_data_len;
  unsigned char *sign_cert_data;
  int sign_cert_data_len;
  unsigned char device_id[MAX_DEVICE_ID_LEN]; // Stores the device ID
  int device_id_len; // Stores the length of the device ID
};

typedef struct device_context_s device_context_t;

// Global static device context handle
static device_context_t *g_device_handle = NULL;

int device_init(void) {
  if (g_device_handle != NULL) {
    // Already initialized or not cleaned up properly
    fprintf(
        stderr,
        "Device: Already initialized or previous instance not cleaned up.\n");
    // Optionally, could try to clean up and re-initialize, or just return an
    // error. For now, let's return an error indicating it's already
    // initialized.
    return CSR_SYSTEM_ERR; // Or a more specific error like
                           // CSR_ERR_ALREADY_INITIALIZED
  }

  const task_config_t *config = get_global_config();
  if (config == NULL) {
    // This should not happen if initialize_global_config is called at startup
    fprintf(stderr, "Device: Global config not initialized.\n");
    return CSR_SYSTEM_ERR;
  }

  g_device_handle = (device_context_t *)malloc(sizeof(device_context_t));
  if (g_device_handle == NULL) {
    return CSR_ERR_MALLOC;
  }
  g_device_handle->handle = NULL;
  g_device_handle->login_type = CS_USERADMIN;
  g_device_handle->enc_cert_data = NULL;
  g_device_handle->enc_cert_data_len = 0;
  g_device_handle->sign_cert_data = NULL;
  g_device_handle->sign_cert_data_len = 0;
  g_device_handle->device_id_len = 0;
  memset(g_device_handle->device_id, 0, MAX_DEVICE_ID_LEN);

  int rv;

  fprintf(stdout, "Device: Starting device initialization (device_no: %d)...\n",
          config->csl.device_no);

  fprintf(stdout, "Device: Opening device (device_no: %d)...\n",
          config->csl.device_no);
  rv = CPCI_OpenDevice(&(g_device_handle->handle), config->csl.device_no, 0,
                       0, CS_NORMAL);
  if (rv != CSR_OK) {
    fprintf(stderr,
            "Device: Failed to open device (device_no: %d), error: 0x%X\n",
            config->csl.device_no, rv);
    free(g_device_handle);
    g_device_handle = NULL;
    return rv;
  }
  fprintf(stdout, "Device: Device opened successfully (device_no: %d).\n",
          config->csl.device_no);

  fprintf(stdout,
          "Device: Logging into device (login_type: CS_USERADMIN)...\n");
  unsigned int pin_len = strlen(config->csl.pin);
  rv = CPCI_LoginDevice(g_device_handle->handle, CS_USERADMIN,
                        (char *)config->csl.pin, pin_len, NULL);
  if (rv != CSR_OK) {
    fprintf(stderr,
            "Device: Failed to login device (login_type: CS_USERADMIN), error: "
            "0x%X\n",
            rv);
    // Attempt to close device on login failure
    CPCI_CloseDevice(g_device_handle->handle);
    free(g_device_handle);
    g_device_handle = NULL;
    return rv;
  }
  fprintf(stdout, "Device: Device login successful.\n");

  fprintf(stdout, "Device: Attempting to get device ID...\n");
  // Set buffer size for CPCI_GetDeviceID
  g_device_handle->device_id_len = MAX_DEVICE_ID_LEN;
  rv = CPCI_GetDeviceID(g_device_handle->handle, g_device_handle->device_id,
                        &(g_device_handle->device_id_len), 0);
  if (rv != CSR_OK) {
    fprintf(
        stderr,
        "Device: Failed to get device ID during initialization, error: 0x%X\n",
        rv);
    // Cleanup already opened/logged-in device
    CPCI_LogoutDevice(g_device_handle->handle, g_device_handle->login_type);
    CPCI_CloseDevice(g_device_handle->handle);
    free(g_device_handle);
    g_device_handle = NULL;
    return rv;
  }
  fprintf(stdout,
          "Device: Device ID retrieved successfully during initialization "
          "(len: %d).\n",
          g_device_handle->device_id_len);

  fprintf(stdout, "Device: Device initialization completed successfully.\n");
  return CSR_OK;
}

void *device_get_api_handle(void) {
  if (g_device_handle != NULL && g_device_handle->handle != NULL) {
    return g_device_handle->handle;
  }
  fprintf(stderr, "Device: Device not initialized or API handle is NULL.\n");
  return NULL;
}

int device_get_id(unsigned char *device_id_buf, int *device_id_len) {
  if (g_device_handle == NULL) {
    fprintf(stderr,
            "Device: Device context not initialized for getting device ID.\n");
    return CSR_ERR_DEVICE_HANDLE;
  }

  if (device_id_buf == NULL || device_id_len == NULL) {
    fprintf(stderr,
            "Device: Invalid output parameters for getting device ID.\n");
    return CSR_INVALIDPARAMETER;
  }

  if (g_device_handle->device_id_len == 0) {
    fprintf(stderr, "Device: Device ID not available in context (was it "
                    "initialized correctly?).\n");
    return CSR_SYSTEM_ERR;
  }

  if (*device_id_len < g_device_handle->device_id_len) {
    fprintf(stderr,
            "Device: Output buffer too small for device ID (provided: %d, "
            "needed: %d).\n",
            *device_id_len, g_device_handle->device_id_len);
    // Inform caller of required size
    *device_id_len = g_device_handle->device_id_len;
    return CSR_BADLENGTH;
  }

  memcpy(device_id_buf, g_device_handle->device_id,
         g_device_handle->device_id_len);
  *device_id_len = g_device_handle->device_id_len;

  fprintf(stdout,
          "Device: Device ID retrieved from context successfully (len: %d).\n",
          *device_id_len);
  return CSR_OK;
}

int device_cert_parse_public_key(const unsigned char *cert_data,
                                 unsigned int cert_len,
                                 unsigned char *output_data,
                                 unsigned int *output_len) {
  unsigned int field_type = CERT_FIELD_PUBLIC_KEY;
  if (g_device_handle == NULL || g_device_handle->handle == NULL) {
    fprintf(stderr,
            "Device: Device not initialized for certificate parsing.\n");
    return CSR_ERR_DEVICE_HANDLE;
  }

  if (cert_data == NULL || cert_len == 0) {
    fprintf(stderr, "Device: Invalid certificate data for parsing.\n");
    return CSR_INVALIDPARAMETER;
  }
  if (output_data == NULL || output_len == NULL) {
    fprintf(stderr,
            "Device: Invalid output parameters for certificate parsing.\n");
    return CSR_INVALIDPARAMETER;
  }
  if (*output_len == 0) {
    fprintf(stderr, "Device: Output buffer length is zero.\n");
    return CSR_INVALIDPARAMETER;
  }

  fprintf(stdout, "Device: Parsing certificate (len: %u, field: 0x%X)...\n",
          cert_len, field_type);

  int rv = CPCI_CertParse(g_device_handle->handle, (unsigned char *)cert_data,
                          cert_len, field_type, output_data, output_len);

  if (rv != CSR_OK) {
    fprintf(stderr,
            "Device: Certificate parsing failed (field: 0x%X), error: 0x%X\n",
            field_type, rv);
    return rv;
  }

  fprintf(
      stdout,
      "Device: Certificate parsing successful (field: 0x%X, output_len: %u).\n",
      field_type, *output_len);
  return CSR_OK;
}

static int read_device_data(const char *data_name_key, unsigned char **buffer,
                            int *len) {
  if (g_device_handle == NULL || g_device_handle->handle == NULL) {
    return CSR_ERR_DEVICE_HANDLE;
  }
  if (data_name_key == NULL || strlen(data_name_key) == 0) {
    fprintf(stderr, "Device: data_name_key is null or empty.\n");
    return CSR_INVALIDPARAMETER;
  }
  if (buffer == NULL || len == NULL) {
    return CSR_INVALIDPARAMETER;
  }

  *buffer = NULL;
  *len = 0;
  int rv;

  *buffer = (unsigned char *)malloc(MAX_NAME_LEN);
  if (*buffer == NULL) {
    fprintf(stderr,
            "Device: Failed to allocate memory (%d bytes) for %s data.\n",
            MAX_NAME_LEN, data_name_key);
    return CSR_ERR_MALLOC;
  }

  int actual_len_read = MAX_NAME_LEN; // Pass buffer capacity to API
  rv = CPCI_ReadDataFromDevice(g_device_handle->handle, data_name_key, *buffer,
                               &actual_len_read);

  if (rv != CSR_OK) {
    fprintf(stderr, "Device: Failed to read data for %s, error: 0x%X\n",
            data_name_key, rv);
    free(*buffer);
    *buffer = NULL;
    // actual_len_read might contain required size if error indicates buffer too
    // small, but we cannot act on it due to "one call" rule.
    return rv;
  }

  if (actual_len_read <= 0) {
    fprintf(stderr,
            "Device: Data read for %s resulted in non-positive length: %d.\n",
            data_name_key, actual_len_read);
    free(*buffer);
    *buffer = NULL;
    return CSR_SYSTEM_ERR;
  }

  // Safeguard: CPCI_ReadDataFromDevice should not report success if it wrote
  // more than the buffer size. If actual_len_read (output) > MAX_NAME_LEN
  // (input buffer size), it implies an API issue or that data was truncated by
  // the device/API without an error code.
  if (actual_len_read > MAX_NAME_LEN) {
    fprintf(stderr,
            "Device: Data read for %s reported length %d, exceeding allocated "
            "buffer %d. Potential buffer overflow or API misbehavior.\n",
            data_name_key, actual_len_read, MAX_NAME_LEN);
    free(*buffer);
    *buffer = NULL;
    return CSR_SYSTEM_ERR;
  }

  *len = actual_len_read;

  // "Truncate" buffer to actual size by reallocating.
  // If realloc fails, the original buffer (*buffer) is still valid and contains
  // the data, but it's oversized. This is generally acceptable.
  if (*len < MAX_NAME_LEN) { // Only realloc if smaller, to potentially save memory
    unsigned char *realloc_buf = (unsigned char *)realloc(*buffer, *len);
    if (realloc_buf == NULL) {
      // realloc failed to shrink. Original buffer is still valid.
      // This can happen if *len is > 0 and memory is tight.
      fprintf(stderr,
              "Device: Warning - failed to reallocate buffer for %s to actual "
              "size %d (from %d). Using oversized buffer.\n",
              data_name_key, *len, MAX_NAME_LEN);
      // *buffer still points to the original, larger allocation. *len is
      // correct.
    } else {
      *buffer = realloc_buf;
    }
  }
  // If *len == MAX_NAME_LEN, no realloc is strictly necessary.

  return CSR_OK;
}

static int load_cert_internal(
    device_context_t *ctx, const char *cert_config_name,
    unsigned char **cert_data_ptr, // Pointer to the cert data field in ctx
    int *cert_data_len_ptr,        // Pointer to the cert len field in ctx
    const char *cert_type_str) {
  if (ctx == NULL || ctx->handle == NULL) {
    fprintf(
        stderr,
        "Device: Device context not initialized for loading %s certificate.\n",
        cert_type_str);
    return CSR_ERR_DEVICE_HANDLE;
  }

  if (cert_config_name == NULL || strlen(cert_config_name) == 0) {
    fprintf(stderr, "Device: %s certificate name (%s) is not configured.\n",
            cert_type_str, cert_config_name ? cert_config_name : "NULL");
    return CSR_INVALIDPARAMETER;
  }

  // Clear any previously loaded certificate data for this type in the context
  if (*cert_data_ptr != NULL) {
    free(*cert_data_ptr);
    *cert_data_ptr = NULL;
    *cert_data_len_ptr = 0;
  }

  int rv = read_device_data(cert_config_name, cert_data_ptr, cert_data_len_ptr);
  if (rv != CSR_OK) {
    fprintf(stderr, "Device: Failed to load %s certificate '%s'. Error: 0x%X\n",
            cert_type_str, cert_config_name, rv);
    // read_device_data frees *cert_data_ptr internally on failure after
    // allocation.
    return rv;
  }
  fprintf(stdout,
          "Device: %s certificate ('%s') loaded successfully into context.\n",
          cert_type_str, cert_config_name);
  return CSR_OK;
}

int device_load_global_certs(void) {
  if (g_device_handle == NULL) {
    fprintf(
        stderr,
        "Device: Device context not initialized for loading certificates.\n");
    return CSR_ERR_DEVICE_HANDLE;
  }

  int rv;
  const task_config_t *config = get_global_config();
  if (config == NULL) {
    fprintf(
        stderr,
        "Device: Global config not initialized for loading certificates.\n");
    return CSR_SYSTEM_ERR;
  }

  rv = load_cert_internal(g_device_handle, config->csl.enc_cert_name,
                          &g_device_handle->enc_cert_data,
                          &g_device_handle->enc_cert_data_len, "encryption");
  if (rv != CSR_OK) {
    return rv;
  }

  rv = load_cert_internal(g_device_handle, config->csl.sign_cert_name,
                          &g_device_handle->sign_cert_data,
                          &g_device_handle->sign_cert_data_len, "signing");
  if (rv != CSR_OK) {
    // Must clean up the successfully loaded encryption certificate if signing
    // cert fails.
    if (g_device_handle->enc_cert_data != NULL) {
      free(g_device_handle->enc_cert_data);
      g_device_handle->enc_cert_data = NULL;
      g_device_handle->enc_cert_data_len = 0;
      fprintf(stdout, "Device: Cleaned up encryption certificate from context "
                      "due to signing certificate load failure.\n");
    }
    return rv;
  }

  return CSR_OK;
}

int get_enc_cert_data(const unsigned char **data, int *len) {
  if (g_device_handle == NULL) {
    fprintf(stderr, "Device: Device context not initialized for getting "
                    "encryption certificate.\n");
    return CSR_ERR_DEVICE_HANDLE;
  }
  if (data == NULL || len == NULL) {
    return CSR_INVALIDPARAMETER;
  }
  if (g_device_handle->enc_cert_data == NULL ||
      g_device_handle->enc_cert_data_len == 0) {
    fprintf(stderr, "Device: Encryption certificate data not loaded or empty "
                    "in context.\n");
    return CSR_SYSTEM_ERR;
  }
  *data = g_device_handle->enc_cert_data;
  *len = g_device_handle->enc_cert_data_len;
  return CSR_OK;
}

int get_sign_cert_data(const unsigned char **data, int *len) {
  if (g_device_handle == NULL) {
    fprintf(stderr, "Device: Device context not initialized for getting "
                    "signing certificate.\n");
    return CSR_ERR_DEVICE_HANDLE;
  }
  if (data == NULL || len == NULL) {
    return CSR_INVALIDPARAMETER;
  }
  if (g_device_handle->sign_cert_data == NULL ||
      g_device_handle->sign_cert_data_len == 0) {
    fprintf(
        stderr,
        "Device: Signing certificate data not loaded or empty in context.\n");
    return CSR_SYSTEM_ERR;
  }
  *data = g_device_handle->sign_cert_data;
  *len = g_device_handle->sign_cert_data_len;
  return CSR_OK;
}

static void device_cleanup_context_certs(void) {
  if (g_device_handle == NULL) {
    return;
  }
  if (g_device_handle->enc_cert_data != NULL) {
    free(g_device_handle->enc_cert_data);
    g_device_handle->enc_cert_data = NULL;
    g_device_handle->enc_cert_data_len = 0;
  }
  if (g_device_handle->sign_cert_data != NULL) {
    free(g_device_handle->sign_cert_data);
    g_device_handle->sign_cert_data = NULL;
    g_device_handle->sign_cert_data_len = 0;
  }
  fprintf(stdout, "Device: Certificates in context cleaned up.\n");
}

int device_cleanup(void) {
  if (g_device_handle == NULL) {
    return CSR_OK;
  }

  device_cleanup_context_certs();

  int rv_logout = CSR_OK;
  int rv_close = CSR_OK;

  if (g_device_handle->handle != NULL) {
    rv_logout =
        CPCI_LogoutDevice(g_device_handle->handle, g_device_handle->login_type);

    rv_close = CPCI_CloseDevice(g_device_handle->handle);
    g_device_handle->handle = NULL; // Avoid double close if called again
  }

  free(g_device_handle);
  g_device_handle = NULL;

  if (rv_logout != CSR_OK) {
    fprintf(stderr, "Device: Logout failed during cleanup, error: 0x%X\n",
            rv_logout);
    // Continue to report close error if it also occurs
  }
  if (rv_close != CSR_OK) {
    fprintf(stderr, "Device: Close device failed during cleanup, error: 0x%X\n",
            rv_close);
  }

  if (rv_logout != CSR_OK)
    return rv_logout; // Prioritize logout error
  if (rv_close != CSR_OK)
    return rv_close; // Then close error

  fprintf(stdout, "Device: Cleanup successful.\n");
  return CSR_OK;
}
