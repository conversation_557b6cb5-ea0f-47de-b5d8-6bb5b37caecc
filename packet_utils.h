#ifndef PACKET_UTILS_H
#define PACKET_UTILS_H

#include "hex_utils.h" // For hex printing functions
#include <arpa/inet.h> // For inet_ntoa
#include <netinet/in.h>
#include <netinet/ip.h>  // For IP header
#include <netinet/tcp.h> // For TCP header
#include <netinet/udp.h> // For UDP header
#include <stdio.h>
#include <stdlib.h>

// Function to extract TCP destination port
uint16_t extract_tcp_dest_port(const uint8_t *packet_buffer, size_t packet_len);

// Function to print TCP flags
void print_tcp_flags(const struct tcphdr *tcph);

// Function to print source/destination IP and port from a raw packet
void print_ip_and_ports(const unsigned char *buffer, size_t len,
                        char *src_ip, char *dst_ip,
                        uint16_t *src_port, uint16_t *dst_port);

// Function to print detailed packet information
void print_packet_info(u_int32_t id, unsigned char *payload_data,
                       int payload_len);

#endif // PACKET_UTILS_H
