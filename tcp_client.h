#ifndef TCP_CLIENT_H
#define TCP_CLIENT_H

#include <event2/bufferevent.h>
#include <event2/event.h>
#include <pthread.h>
#include <stddef.h>
#include <stdint.h>

// Forward declaration
typedef struct tcp_client tcp_client_t;

/**
 * @brief Response data processing callback function type
 *
 * @param client TCP client instance
 * @param data Received data
 * @param len Data length
 * @param user_data User-defined data
 */
typedef void (*response_callback_t)(tcp_client_t *client, const uint8_t *data,
                                    size_t len, void *user_data);

/**
 * @brief Connection status callback function type
 *
 * @param client TCP client instance
 * @param connected Connection status, 1 for connected, 0 for disconnected
 * @param user_data User-defined data
 */
typedef void (*connection_callback_t)(tcp_client_t *client, int connected,
                                      void *user_data);

/**
 * @brief TCP client structure
 */
struct tcp_client {
  struct event_base *base;         // libevent event loop
  struct bufferevent *bev;         // Buffered event
  char *host;                      // Server IP address
  int port;                        // Server port
  int connected;                   // Connection status
  response_callback_t response_cb; // Response callback function
  connection_callback_t conn_cb;   // Connection status callback function
  void *user_data;                 // User-defined data
  int auto_reconnect;              // Auto-reconnect flag
  int reconnect_interval;          // Reconnect interval (seconds)
  struct event *reconnect_timer;   // Reconnect timer

  // Synchronization for connection status
  pthread_mutex_t conn_mutex; // Mutex for connection status
  pthread_cond_t conn_cond;   // Condition variable for connection status
  int conn_result; // Connection result: 1=connected, 0=disconnected, -1=error
};

/**
 * @brief Create TCP client instance
 *
 * @param host Server IP address
 * @param port Server port
 * @param base libevent event loop, if NULL, a new one will be created
 * @return Returns client instance on success, NULL on failure
 */
tcp_client_t *tcp_client_create(const char *host, int port,
                                struct event_base *base);

/**
 * @brief Set response data processing callback function
 *
 * @param client TCP client instance
 * @param callback Callback function
 * @param user_data User-defined data
 */
void tcp_client_set_response_callback(tcp_client_t *client,
                                      response_callback_t callback,
                                      void *user_data);

/**
 * @brief Set connection status callback function
 *
 * @param client TCP client instance
 * @param callback Callback function
 * @param user_data User-defined data
 */
void tcp_client_set_connection_callback(tcp_client_t *client,
                                        connection_callback_t callback,
                                        void *user_data);

/**
 * @brief Set auto-reconnect options
 *
 * @param client TCP client instance
 * @param enable Enable auto-reconnect
 * @param interval Reconnect interval (seconds)
 */
void tcp_client_set_auto_reconnect(tcp_client_t *client, int enable,
                                   int interval);

/**
 * @brief Connect to server
 *
 * @param client TCP client instance
 * @return Returns 0 on success, -1 on failure
 */
int tcp_client_connect(tcp_client_t *client);

/**
 * @brief Send data with timeout
 *
 * @param client TCP client instance
 * @param data Data to send
 * @param len Data length
 * @param timeout_ms Timeout in milliseconds, 0 means no timeout
 * @return Returns 0 on success, -1 on failure, -2 on timeout
 */
int tcp_client_send(tcp_client_t *client, const uint8_t *data, size_t len,
                    int timeout_ms);

/**
 * @brief Send data without timeout (for backward compatibility)
 *
 * This macro provides backward compatibility for code that doesn't specify
 * timeout. It will wait indefinitely for connection to be established.
 */
#define tcp_client_send_simple(client, data, len)                              \
  tcp_client_send(client, data, len, 0)

/**
 * @brief Disconnect from server
 *
 * @param client TCP client instance
 */
void tcp_client_disconnect(tcp_client_t *client);

/**
 * @brief Destroy TCP client instance
 *
 * @param client TCP client instance
 */
void tcp_client_destroy(tcp_client_t *client);

/**
 * @brief Check if client is connected
 *
 * @param client TCP client instance
 * @return Connection status, 1 for connected, 0 for disconnected
 */
int tcp_client_is_connected(tcp_client_t *client);

/**
 * @brief Run event loop (blocking)
 *
 * @param client TCP client instance
 * @return Event loop exit code
 */
int tcp_client_run(tcp_client_t *client);

/**
 * @brief Stop event loop
 *
 * @param client TCP client instance
 */
void tcp_client_stop(tcp_client_t *client);

#endif // TCP_CLIENT_H