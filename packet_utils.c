#define _GNU_SOURCE // Enable GNU extensions
#include "packet_utils.h"
#include <stdlib.h>
#include <string.h>

static void process_tcp_packet(unsigned char *tcp_header_base,
                               int remaining_payload_len) {
  if (remaining_payload_len < sizeof(struct tcphdr)) {
    printf("Packet too short for even a minimal TCP header "
           "(remaining_payload_len: %d, needed: %zu).\n",
           remaining_payload_len, sizeof(struct tcphdr));
    return;
  }

  struct tcphdr *tcph = (struct tcphdr *)tcp_header_base;
  printf("Original Destination Port (TCP): %u\n", ntohs(tcph->dest));

  if (tcph->doff < 5) {
    printf("Invalid TCP header data offset: doff %u is less than minimum 5.\n",
           tcph->doff);
    return;
  }
  unsigned int tcp_header_actual_len = tcph->doff * 4;
  if (tcp_header_actual_len > remaining_payload_len) {
    printf("TCP header length from doff (doff: %u => %u bytes) exceeds "
           "remaining packet length (%d).\n",
           tcph->doff, tcp_header_actual_len, remaining_payload_len);
    return;
  }

  print_tcp_flags(tcph);
}

static void process_udp_packet(unsigned char *udp_header_base,
                               int remaining_payload_len_for_udp) {
  if (remaining_payload_len_for_udp < sizeof(struct udphdr)) {
    printf("Packet too short for UDP header (remaining_payload_len: %d, "
           "needed: %zu).\n",
           remaining_payload_len_for_udp, sizeof(struct udphdr));
    return;
  }

  struct udphdr *udph = (struct udphdr *)udp_header_base;
  printf("Original Destination Port (UDP): %u\n", ntohs(udph->dest));
}

static void process_ipv4_packet(struct iphdr *iph,
                                unsigned char *ip_packet_start,
                                int ip_packet_len) {
  char dst_ip_str[INET_ADDRSTRLEN];
  struct in_addr dest_addr;

  dest_addr.s_addr = iph->daddr;
  inet_ntop(AF_INET, &dest_addr, dst_ip_str, INET_ADDRSTRLEN);
  printf("Original Destination IP: %s\n", dst_ip_str);

  unsigned int ip_header_len = iph->ihl * 4;
  unsigned char *transport_header_base = ip_packet_start + ip_header_len;
  int remaining_len_for_transport = ip_packet_len - ip_header_len;

  if (iph->protocol == IPPROTO_TCP) {
    process_tcp_packet(transport_header_base, remaining_len_for_transport);
  } else if (iph->protocol == IPPROTO_UDP) {
    process_udp_packet(transport_header_base, remaining_len_for_transport);
  } else {
    printf("Protocol: Other (%u)\n", iph->protocol);
  }
}

void print_tcp_flags(const struct tcphdr *tcph) {
  if (!tcph) {
    printf("TCP Header not available for flag printing.\n");
    return;
  }

  printf("TCP Flags: ");
  if (tcph->fin)
    printf("FIN ");
  if (tcph->syn)
    printf("SYN ");
  if (tcph->rst)
    printf("RST ");
  if (tcph->psh)
    printf("PSH ");
  if (tcph->ack)
    printf("ACK ");
  if (tcph->urg)
    printf("URG ");
  printf("\n");
}

void print_packet_info(u_int32_t id, unsigned char *payload_data,
                       int payload_len) {
  printf("Received packet with ID: %u\n", id);

  if (payload_len < 0) {
    printf("Couldn't get payload (invalid length: %d)\n", payload_len);
    return;
  }

  if (payload_len < sizeof(struct iphdr)) {
    printf("Packet too short for even a minimal IP header (len: %d, needed: "
           "%zu).\n",
           payload_len, sizeof(struct iphdr));
    return;
  }

  struct iphdr *iph = (struct iphdr *)payload_data;

  if (iph->ihl < 5) {
    printf("Invalid IP header length: ihl %u is less than minimum 5.\n",
           iph->ihl);
    return;
  }
  unsigned int ip_header_actual_len = iph->ihl * 4;
  if (ip_header_actual_len > payload_len) {
    printf("IP header length (%u bytes) from ihl (%u) exceeds total packet "
           "data length (%d bytes).\n",
           ip_header_actual_len, iph->ihl, payload_len);
    return;
  }

  if (iph->version == 4) {
    process_ipv4_packet(iph, payload_data, payload_len);
  } else {
    printf("Not an IPv4 packet (version: %u).\n", iph->version);
  }
}

uint16_t extract_tcp_dest_port(const uint8_t *packet_buffer,
                               size_t packet_len) {
  if (packet_buffer == NULL ||
      packet_len < sizeof(struct ip) + sizeof(struct tcphdr)) {
    fprintf(stderr, "extract_tcp_dest_port: Invalid packet buffer or length "
                    "too short for IP+TCP header.\n");
    return 0;
  }

  const struct ip *iph = (const struct ip *)packet_buffer;

  if (iph->ip_v != 4) {
    fprintf(stderr, "extract_tcp_dest_port: Not an IPv4 packet.\n");
    return 0;
  }

  if (iph->ip_p != IPPROTO_TCP) {
    fprintf(stderr, "extract_tcp_dest_port: Not a TCP packet (protocol: %u).\n",
            iph->ip_p);
    return 0;
  }

  unsigned int ip_header_len = iph->ip_hl * 4;
  if (ip_header_len < sizeof(struct ip)) {
    fprintf(stderr,
            "extract_tcp_dest_port: Invalid IP header length: %u bytes.\n",
            ip_header_len);
    return 0;
  }

  if (packet_len < ip_header_len + sizeof(struct tcphdr)) {
    fprintf(
        stderr,
        "extract_tcp_dest_port: Packet too short for IP header and TCP header "
        "(packet_len: %zu, ip_header_len: %u, tcp_header_min_len: %zu).\n",
        packet_len, ip_header_len, sizeof(struct tcphdr));
    return 0;
  }

  const struct tcphdr *tcph =
      (const struct tcphdr *)(packet_buffer + ip_header_len);

  unsigned int tcp_header_actual_len = tcph->th_off * 4;
  if (tcp_header_actual_len < sizeof(struct tcphdr)) {
    fprintf(stderr,
            "extract_tcp_dest_port: Invalid TCP header data offset: %u "
            "(actual_len: %u bytes).\n",
            tcph->th_off, tcp_header_actual_len);
    return 0;
  }

  if (packet_len < ip_header_len + tcp_header_actual_len) {
    fprintf(
        stderr,
        "extract_tcp_dest_port: Packet too short for actual TCP header "
        "(packet_len: %zu, ip_header_len: %u, tcp_header_actual_len: %u).\n",
        packet_len, ip_header_len, tcp_header_actual_len);
    return 0;
  }

  return ntohs(tcph->th_dport);
}

void print_ip_and_ports(const unsigned char *buffer, size_t len,
                        char *src_ip, char *dst_ip,
                        uint16_t *src_port, uint16_t *dst_port) {
  if (src_ip) src_ip[0] = '\0';
  if (dst_ip) dst_ip[0] = '\0';
  if (src_port) *src_port = 0;
  if (dst_port) *dst_port = 0;

  if (buffer == NULL) {
    printf("print_ip_and_ports: Buffer is NULL\n");
    return;
  }

  if (len < sizeof(struct iphdr)) {
    printf("print_ip_and_ports: Packet too short for IP header (len: %zu, "
           "needed: %zu).\n",
           len, sizeof(struct iphdr));
    return;
  }

  struct iphdr *iph = (struct iphdr *)buffer;

  if (iph->ihl < 5) {
    printf("print_ip_and_ports: Invalid IP header length: ihl %u is less than "
           "minimum 5.\n",
           iph->ihl);
    return;
  }
  unsigned int ip_header_actual_len = iph->ihl * 4;
  if (ip_header_actual_len > len) {
    printf("print_ip_and_ports: IP header length (%u bytes) from ihl (%u) "
           "exceeds total packet data length (%zu bytes).\n",
           ip_header_actual_len, iph->ihl, len);
    return;
  }

  if (iph->version == 4) {
    char src_ip_str[INET_ADDRSTRLEN];
    char dst_ip_str[INET_ADDRSTRLEN];
    struct in_addr src_addr, dst_addr;

    src_addr.s_addr = iph->saddr;
    dst_addr.s_addr = iph->daddr;

    inet_ntop(AF_INET, &src_addr, src_ip_str, INET_ADDRSTRLEN);
    inet_ntop(AF_INET, &dst_addr, dst_ip_str, INET_ADDRSTRLEN);

    if (src_ip) {
      strncpy(src_ip, src_ip_str, INET_ADDRSTRLEN - 1);
      src_ip[INET_ADDRSTRLEN - 1] = '\0';
    }
    if (dst_ip) {
      strncpy(dst_ip, dst_ip_str, INET_ADDRSTRLEN - 1);
      dst_ip[INET_ADDRSTRLEN - 1] = '\0';
    }

    printf("Source IP: %s, Destination IP: %s\n", src_ip_str, dst_ip_str);

    unsigned char *transport_header_base =
        (unsigned char *)buffer + ip_header_actual_len;
    int remaining_len_for_transport = len - ip_header_actual_len;

    if (iph->protocol == IPPROTO_TCP) {
      if (remaining_len_for_transport < sizeof(struct tcphdr)) {
        printf("print_ip_and_ports: Packet too short for TCP header "
               "(remaining: %d, needed: %zu).\n",
               remaining_len_for_transport, sizeof(struct tcphdr));
        return;
      }
      struct tcphdr *tcph = (struct tcphdr *)transport_header_base;
      uint16_t tcp_src_port = ntohs(tcph->source);
      uint16_t tcp_dst_port = ntohs(tcph->dest);

      if (src_port) *src_port = tcp_src_port;
      if (dst_port) *dst_port = tcp_dst_port;

      printf("Source Port (TCP): %u, Destination Port (TCP): %u\n",
             tcp_src_port, tcp_dst_port);
    } else if (iph->protocol == IPPROTO_UDP) {
      if (remaining_len_for_transport < sizeof(struct udphdr)) {
        printf("print_ip_and_ports: Packet too short for UDP header "
               "(remaining: %d, needed: %zu).\n",
               remaining_len_for_transport, sizeof(struct udphdr));
        return;
      }
      struct udphdr *udph = (struct udphdr *)transport_header_base;
      uint16_t udp_src_port = ntohs(udph->source);
      uint16_t udp_dst_port = ntohs(udph->dest);

      if (src_port) *src_port = udp_src_port;
      if (dst_port) *dst_port = udp_dst_port;

      printf("Source Port (UDP): %u, Destination Port (UDP): %u\n",
             udp_src_port, udp_dst_port);
    } else {
      printf("Transport Protocol: Other (%u)\n", iph->protocol);
    }
  } else {
    printf("print_ip_and_ports: Not an IPv4 packet (version: %u).\n",
           iph->version);
  }
}
