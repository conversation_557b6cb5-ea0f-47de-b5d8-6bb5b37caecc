#include "auth_server.h"
#include "auth_handler.h"
#include "config_handler.h"
#include "heartbeat_handler.h"
#include "message_common.h"
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/socket.h>

#include <event2/buffer.h>
#include <event2/bufferevent.h>
#include <event2/event.h>
#include <event2/listener.h>

typedef struct {
  const task_config_t *config;
} server_thread_args_t;

static void read_cb(struct bufferevent *bev, void *ctx);
static void event_cb(struct bufferevent *bev, short events, void *ctx);
static void accept_conn_cb(struct evconnlistener *listener, evutil_socket_t fd,
                           struct sockaddr *address, int socklen, void *ctx);
static void accept_error_cb(struct evconnlistener *listener, void *ctx);

static int validate_message_header(const message_header_t *header,
                                   struct bufferevent *bev);
static int validate_payload_length(uint32_t host_len, uint32_t host_type,
                                   struct bufferevent *bev);
static int process_complete_message(struct bufferevent *bev,
                                    const message_header_t *header,
                                    uint32_t host_len, uint32_t host_type);
static int handle_incomplete_message(struct bufferevent *bev, size_t buffer_len,
                                     uint32_t total_message_size);

static void *auth_server_thread_func(void *arg) {
  server_thread_args_t *thread_args = (server_thread_args_t *)arg;
  const task_config_t *config = thread_args->config;
  struct event_base *base;
  struct evconnlistener *listener;
  struct sockaddr_in sin;

  base = event_base_new();
  if (!base) {
    perror("Couldn\'t open event base");
    free(thread_args);
    return NULL;
  }

  memset(&sin, 0, sizeof(sin));
  sin.sin_family = AF_INET;
  sin.sin_port = htons(config->auth_server.port);

  if (inet_pton(AF_INET, config->auth_server.listen_ip, &sin.sin_addr) <= 0) {
    fprintf(stderr, "Invalid listen_ip: %s\n", config->auth_server.listen_ip);
    event_base_free(base);
    free(thread_args);
    return NULL;
  }

  listener = evconnlistener_new_bind(base, accept_conn_cb, NULL,
                                     LEV_OPT_CLOSE_ON_FREE | LEV_OPT_REUSEABLE,
                                     -1, (struct sockaddr *)&sin, sizeof(sin));

  if (!listener) {
    perror("Couldn\'t create listener");
    event_base_free(base);
    free(thread_args);
    return NULL;
  }
  evconnlistener_set_error_cb(listener, accept_error_cb);

  printf("Auth server listening on %s:%d\n", config->auth_server.listen_ip,
         config->auth_server.port);
  event_base_dispatch(base);

  evconnlistener_free(listener);
  event_base_free(base);
  free(thread_args);
  printf("Auth server thread finished.\n");
  return NULL;
}

int start_auth_server(const task_config_t *config) {
  if (!config) {
    fprintf(stderr, "Configuration is NULL\n");
    return -1;
  }
  if (config->auth_server.listen_ip[0] == '\0') {
    fprintf(stderr, "Listen IP is NULL in configuration\n");
    return -1;
  }
  if (config->auth_server.port <= 0 || config->auth_server.port > 65535) {
    fprintf(stderr, "Invalid port number: %d\n", config->auth_server.port);
    return -1;
  }

  pthread_t tid;
  server_thread_args_t *thread_args = malloc(sizeof(server_thread_args_t));
  if (!thread_args) {
    perror("Failed to allocate memory for thread arguments");
    return -1;
  }
  thread_args->config = config;

  if (pthread_create(&tid, NULL, auth_server_thread_func, thread_args) != 0) {
    perror("Failed to create auth server thread");
    free(thread_args);
    return -1;
  }

  if (pthread_detach(tid) != 0) {
    perror("Failed to detach auth server thread");
  }

  printf("Auth server thread started.\n");
  return 0;
}

static void accept_conn_cb(struct evconnlistener *listener, evutil_socket_t fd,
                           struct sockaddr *address, int socklen, void *ctx) {
  struct event_base *base = evconnlistener_get_base(listener);
  struct bufferevent *bev =
      bufferevent_socket_new(base, fd, BEV_OPT_CLOSE_ON_FREE);

  if (!bev) {
    fprintf(stderr, "Error constructing bufferevent!");
    event_base_loopbreak(base); // Consider a more graceful shutdown
    return;
  }

  bufferevent_setcb(bev, read_cb, NULL, event_cb, NULL);
  bufferevent_enable(bev, EV_READ | EV_WRITE);

  char client_ip[INET_ADDRSTRLEN];
  struct sockaddr_in *client_addr = (struct sockaddr_in *)address;
  inet_ntop(AF_INET, &client_addr->sin_addr, client_ip, INET_ADDRSTRLEN);
  printf("Accepted connection from %s:%d\n", client_ip,
         ntohs(client_addr->sin_port));
}

static int validate_message_header(const message_header_t *header,
                                   struct bufferevent *bev) {
  if (strncmp(header->magic, MAGIC_NUMBER, MAGIC_LEN) != 0) {
    fprintf(stderr,
            "Invalid magic number. Expected '%.4s', got '%.4s'. Closing "
            "connection.\n",
            MAGIC_NUMBER, header->magic);
    return -1;
  }

  int32_t signed_len = header->len;
  int32_t signed_type = header->type;

  if (signed_len < 0) {
    fprintf(stderr,
            "Negative payload length %d in header. Invalid protocol data. "
            "Closing connection.\n",
            signed_len);
    return -1;
  }

  if (signed_type < 0) {
    fprintf(stderr,
            "Negative message type %d in header. Invalid protocol data. "
            "Closing connection.\n",
            signed_type);
    return -1;
  }

  return 0;
}

static int validate_payload_length(uint32_t host_len, uint32_t host_type,
                                   struct bufferevent *bev) {
  if (host_len > MAX_PAYLOAD_SIZE) {
    fprintf(stderr,
            "Payload length %u exceeds maximum allowed size %d. Possible "
            "attack or corrupted data. Closing connection.\n",
            host_len, MAX_PAYLOAD_SIZE);
    return -1;
  }

  switch (host_type) {
  case MSG_TYPE_HEARTBEAT:
    return validate_heartbeat_payload(host_len);
  case MSG_TYPE_AUTHENTICATION:
    return validate_auth_payload(host_len);
  case MSG_TYPE_CONFIG:
    return validate_config_payload(host_len);
  default:
    if (host_len > MAX_UNKNOWN_PAYLOAD_SIZE) {
      fprintf(stderr,
              "Unknown message type %u with large payload %u. Closing "
              "connection.\n",
              host_type, host_len);
      return -1;
    }
    break;
  }

  return 0;
}

static int handle_incomplete_message(struct bufferevent *bev, size_t buffer_len,
                                     uint32_t total_message_size) {
  printf("Incomplete message. Have %zu, need %u. Waiting for more data.\n",
         buffer_len, total_message_size);

  if (buffer_len > MAX_PAYLOAD_SIZE + sizeof(message_header_t)) {
    fprintf(stderr,
            "Buffer size %zu exceeds maximum allowed for incomplete messages. "
            "Possible memory exhaustion attack. Closing connection.\n",
            buffer_len);
    return -1;
  }

  if (total_message_size > LARGE_MESSAGE_THRESHOLD &&
      buffer_len < total_message_size / 2) {
    fprintf(stderr,
            "Suspicious incomplete large message (size %u, have %zu). "
            "Potential slowloris attack. Closing connection.\n",
            total_message_size, buffer_len);
    return -1;
  }

  return 0;
}

static int process_complete_message(struct bufferevent *bev,
                                    const message_header_t *header,
                                    uint32_t host_len, uint32_t host_type) {
  struct evbuffer *input = bufferevent_get_input(bev);
  size_t header_size = sizeof(message_header_t);

  uint32_t total_message_size = header_size + host_len;
  if (total_message_size < header_size) {
    fprintf(stderr,
            "Invalid total message size %u (less than header size %zu). Data "
            "corruption detected. Closing connection.\n",
            total_message_size, header_size);
    return -1;
  }

  if (evbuffer_drain(input, header_size) != 0) {
    fprintf(stderr, "Failed to drain header from buffer\n");
    return -1;
  }

  unsigned char *payload = NULL;
  if (host_len > 0) {
    if (evbuffer_get_length(input) < host_len) {
      fprintf(stderr,
              "Buffer underrun: expected %u bytes for payload, but only %zu "
              "available after draining header. Closing connection.\n",
              host_len, evbuffer_get_length(input));
      return -1;
    }

    payload = malloc(host_len);
    if (!payload) {
      fprintf(stderr,
              "Failed to allocate memory for payload of size %u. Possible "
              "memory exhaustion attack. Closing connection.\n",
              host_len);
      return -1;
    }

    if (evbuffer_remove(input, payload, host_len) != host_len) {
      fprintf(stderr, "Failed to remove payload from buffer\n");
      free(payload);
      return -1;
    }
  }

  printf("Received message: type=%u, payload_len=%u\n", host_type, host_len);

  int result = 0;
  switch (host_type) {
  case MSG_TYPE_HEARTBEAT:
    result = process_heartbeat_message(bev, header, payload, host_len);
    break;
  case MSG_TYPE_AUTHENTICATION:
    result = process_auth_message(bev, header, payload, host_len);
    break;
  case MSG_TYPE_CONFIG:
    result = process_config_message(bev, header, payload, host_len);
    break;
  default:
    fprintf(stderr, "Unknown message type: %u\n", host_type);
    if (host_len > 0 && payload) {
      printf("Unknown payload (len %u):\n", host_len);
    }
    break;
  }

  if (payload) {
    free(payload);
  }

  return result;
}

static void read_cb(struct bufferevent *bev, void *ctx) {

  struct evbuffer *input = bufferevent_get_input(bev);
  size_t buffer_len = evbuffer_get_length(input);
  message_header_t header;
  size_t header_size = sizeof(message_header_t);

  printf("read_cb called, buffer_len: %zu\n", buffer_len);

  while (buffer_len >= header_size) {
    if (evbuffer_copyout(input, &header, header_size) != header_size) {
      fprintf(stderr, "Failed to copyout header data\n");
      bufferevent_free(bev);
      return;
    }

    printf("Peeked header: magic='%.4s', len=%d, type=%d\n", header.magic,
           header.len, header.type);

    if (validate_message_header(&header, bev) != 0) {
      bufferevent_free(bev);
      return;
    }

    uint32_t host_len = header.len;
    uint32_t host_type = header.type;

    if (validate_payload_length(host_len, host_type, bev) != 0) {
      bufferevent_free(bev);
      return;
    }

    if (host_len > SIZE_MAX - header_size) {
      fprintf(stderr,
              "Payload length %u would cause integer overflow when calculating "
              "total message size. Closing connection.\n",
              host_len);
      bufferevent_free(bev);
      return;
    }

    uint32_t total_message_size = header_size + host_len;

    if (total_message_size > MAX_PAYLOAD_SIZE + header_size) {
      fprintf(stderr,
              "Total message size %u is unreasonable. Closing connection.\n",
              total_message_size);
      bufferevent_free(bev);
      return;
    }

    printf("Expected total message size: %u (header %zu + payload %u)\n",
           total_message_size, header_size, host_len);

    if (buffer_len >= total_message_size) {
      if (process_complete_message(bev, &header, host_len, host_type) != 0) {
        bufferevent_free(bev);
        return;
      }

      buffer_len = evbuffer_get_length(input);
    } else {
      if (handle_incomplete_message(bev, buffer_len, total_message_size) != 0) {
        bufferevent_free(bev);
        return;
      }
      break;
    }
  }
}

static void event_cb(struct bufferevent *bev, short events, void *ctx) {
  if (events & BEV_EVENT_ERROR) {
    fprintf(stderr, "Error from bufferevent: %s\n",
            evutil_socket_error_to_string(EVUTIL_SOCKET_ERROR()));
  }
  if (events & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
    printf("Connection closed or error.\n");
    bufferevent_free(bev);
  }
}

static void accept_error_cb(struct evconnlistener *listener, void *ctx) {
  struct event_base *base = evconnlistener_get_base(listener);
  int err = EVUTIL_SOCKET_ERROR();
  fprintf(stderr,
          "Got an error %d (%s) on the listener. "
          "Shutting down.\n",
          err, evutil_socket_error_to_string(err));
  event_base_loopexit(base, NULL);
}
