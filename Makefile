CC=gcc
CFLAGS=-Wall -Wextra -g -O2 -pthread -Wl,-rpath,'$$ORIGIN:$$ORIGIN/lib' -Ilibevent/include
LDLIBS=-Llib -Llibevent/lib -lsitp_lib -lbsk -lnetfilter_queue -lnfnetlink -lpthread -levent_core -levent
TARGET=nfq_proxy

# Control whether to use color for logging. Default is 1 (on).
# Can be disabled from command line: make LOG_USE_COLOR=0
LOG_USE_COLOR ?= 1
ifeq ($(LOG_USE_COLOR), 1)
CFLAGS += -DLOG_USE_COLOR
endif

SRCS = $(wildcard *.c)
OBJS = $(SRCS:.c=.o)

all: $(TARGET)

# Rule to build the main application
$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) -o $(TARGET) $(OBJS) $(LDLIBS)

# Generic rule to compile .c files to .o files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(TARGET) *.o

.PHONY: all clean
