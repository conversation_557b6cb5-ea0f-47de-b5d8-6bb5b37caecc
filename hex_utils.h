#ifndef HEX_UTILS_H
#define HEX_UTILS_H

#include <ctype.h> // For isprint
#include <stdio.h> // For printf

// Function to print data in 2-byte hex groups with ASCII (similar to tcpdump
// -X)
void print_hex_ascii_line(const unsigned char *payload, int len, int offset);

// Function to print packet data
void print_payload(const unsigned char *payload, int len);

// Function to convert a hex string to a byte array
// Returns the number of bytes converted, or -1 on error (e.g., invalid hex
// string)
int hex_string_to_bytes(const char *hex_string, unsigned char *byte_array,
                        size_t max_bytes);

#endif // HEX_UTILS_H
