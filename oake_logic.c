#include "oake_logic.h"
#include <errno.h> // For ETIMEDOUT
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>   // For clock_gettime()
#include <unistd.h> // For sleep()

#include "ip_map.h"
#include "message_common.h"
#include "oake.h"
#include "tcp_client.h"

typedef struct {
  int is_master;
  char peer_ip[MAX_IP_ADDR_LEN]; // Defined in config_loader.h
  int peer_port;
  char host_ip[MAX_IP_ADDR_LEN];

  tcp_client_t *oake_client;
  volatile int response_processed;
  pthread_cond_t response_cond;
  pthread_mutex_t response_mutex;
  oake_generic_data_t oake_data;
} oake_thread_args_t;

static void execute_oake_business_logic(tcp_client_t *client,
                                        oake_thread_args_t *args);

static void set_oake_ip_addresses(oake_generic_data_t *oake_data,
                                  const char *host_ip, const char *peer_ip);

static int init_device_id(unsigned char *device_id_buf,
                          uint32_t *device_id_len) {
  int result = oake_get_device_id(device_id_buf, device_id_len);
  if (result != 0) {
    fprintf(stderr, "OAKE Logic: Failed to get device ID, error code: %d\n",
            result);
    return -1;
  }
  printf("OAKE Logic: Device ID obtained successfully, length: %u\n",
         *device_id_len);
  return 0;
}

static int init_oake_session(const unsigned char *device_id,
                             uint32_t device_id_len,
                             oake_generic_data_t *oake_data) {
  oake_generic_data_t *data_out = oake_data;

  int result = oake_init(device_id, device_id_len, &data_out);
  if (result != 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to initialize OAKE session, error code: %d\n",
            result);
    return -1;
  }
  printf("OAKE Logic: OAKE session initialized successfully\n");
  return 0;
}

static int send_auth_message(tcp_client_t *client, const uint8_t *payload,
                             size_t payload_len) {
  message_header_t header;
  memcpy(header.magic, MAGIC_NUMBER, MAGIC_LEN);
  header.len = (int)payload_len;         // little endian
  header.type = MSG_TYPE_AUTHENTICATION; // little endian

  size_t total_size = sizeof(message_header_t) + payload_len;
  uint8_t *message_buffer = malloc(total_size);
  if (!message_buffer) {
    fprintf(stderr,
            "OAKE Logic: Failed to allocate memory for message buffer\n");
    return -1;
  }

  memcpy(message_buffer, &header, sizeof(message_header_t));
  memcpy(message_buffer + sizeof(message_header_t), payload, payload_len);

  int result = tcp_client_send(client, message_buffer, total_size,
                               5000); // 5 second timeout
  if (result != 0) {
    fprintf(
        stderr,
        "OAKE Logic: Failed to send authentication message, error code: %d\n",
        result);
    free(message_buffer);
    return -1;
  }

  printf("OAKE Logic: Authentication message sent successfully, total size: "
         "%zu bytes\n",
         total_size);
  free(message_buffer);
  return 0;
}

static void oake_response_callback(tcp_client_t *client, const uint8_t *data,
                                   size_t len, void *user_data) {
  oake_thread_args_t *args = (oake_thread_args_t *)user_data;
  printf("OAKE Logic: Received response data, length: %zu bytes\n", len);

  // Check if response already processed (only process once)
  if (args->response_processed) {
    printf("OAKE Logic: Response already processed, ignoring\n");
    return;
  }

  // Validate minimum message size (header)
  if (len < sizeof(message_header_t)) {
    fprintf(stderr, "OAKE Logic: Received data too small for message header\n");
    return;
  }

  message_header_t header;
  memcpy(&header, data, sizeof(message_header_t));

  // Validate magic number
  if (memcmp(header.magic, MAGIC_NUMBER, MAGIC_LEN) != 0) {
    fprintf(stderr, "OAKE Logic: Invalid magic number in response\n");
    return;
  }

  // Validate message type
  if (header.type != MSG_TYPE_AUTHENTICATION) {
    fprintf(stderr, "OAKE Logic: Unexpected message type: %d\n", header.type);
    return;
  }

  // Validate payload length
  if (header.len < 0 ||
      (size_t)header.len != (len - sizeof(message_header_t))) {
    fprintf(stderr,
            "OAKE Logic: Payload length mismatch: expected %d, got %zu\n",
            header.len, len - sizeof(message_header_t));
    return;
  }

  printf(
      "OAKE Logic: Valid response message received, payload size: %d bytes\n",
      header.len);

  const uint8_t *payload = data + sizeof(message_header_t);
  size_t payload_len = header.len;

  printf("OAKE Logic: Deserializing OAKE response data...\n");
  int result = oake_deserialize(payload, payload_len, &args->oake_data);
  if (result != 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to deserialize OAKE response data, error code: "
            "%d\n",
            result);
    return;
  }
  printf("OAKE Logic: OAKE response data deserialized successfully\n");

  printf("OAKE Logic: Initializing OAKE with context...\n");
  result =
      oake_init_with_ctx(args->oake_data.device_id,
                         args->oake_data.device_id_len, args->oake_data.ctx);
  if (result != 0) {
    fprintf(
        stderr,
        "OAKE Logic: Failed to initialize OAKE with context, error code: %d\n",
        result);
    return;
  }
  printf("OAKE Logic: OAKE initialized with context successfully\n");

  printf("OAKE Logic: Creating OAKE acknowledgement...\n");
  result = oake_create_acknowledgement(&args->oake_data);
  if (result != 0) {
    fprintf(
        stderr,
        "OAKE Logic: Failed to create OAKE acknowledgement, error code: %d\n",
        result);
    return;
  }
  printf("OAKE Logic: OAKE acknowledgement created successfully\n");

  set_oake_ip_addresses(&args->oake_data, args->host_ip, args->peer_ip);

  printf("OAKE Logic: Serializing OAKE acknowledgement data...\n");
  size_t ack_serialized_size = oake_get_serialized_size(&args->oake_data);
  if (ack_serialized_size == 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to get acknowledgement serialized size\n");
    return;
  }

  uint8_t *ack_serialized_buffer = malloc(ack_serialized_size);
  if (!ack_serialized_buffer) {
    fprintf(stderr, "OAKE Logic: Failed to allocate memory for acknowledgement "
                    "serialized buffer\n");
    return;
  }

  int ack_serialized_bytes = oake_serialize(
      &args->oake_data, ack_serialized_buffer, ack_serialized_size);
  if (ack_serialized_bytes < 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to serialize OAKE acknowledgement data\n");
    free(ack_serialized_buffer);
    return;
  }
  printf("OAKE Logic: OAKE acknowledgement data serialized successfully, size: "
         "%d bytes\n",
         ack_serialized_bytes);

  printf("OAKE Logic: Sending OAKE acknowledgement message...\n");
  if (send_auth_message(args->oake_client, ack_serialized_buffer,
                        ack_serialized_bytes) != 0) {
    fprintf(stderr, "OAKE Logic: Failed to send acknowledgement message\n");
    free(ack_serialized_buffer);
    return;
  }

  free(ack_serialized_buffer);
  printf("OAKE Logic: OAKE acknowledgement message sent successfully\n");

  printf("OAKE Logic: Finalizing OAKE session...\n");
  result = oake_finalize_session(&args->oake_data);
  if (result != 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to finalize OAKE session, error code: %d\n",
            result);
    return;
  }

  printf("OAKE Logic: OAKE session finalized successfully\n");
  printf("OAKE Logic: OAKE authentication handshake completed\n");

  printf("OAKE Logic: Storing OAKE data in hash table for peer IP: %s\n",
         args->peer_ip);
  result = oake_set_data_by_ip(args->peer_ip, &args->oake_data);
  if (result != 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to store OAKE data by IP, error code: %d\n",
            result);
    return;
  }
  printf("OAKE Logic: OAKE data stored successfully for peer IP: %s\n",
         args->peer_ip);

  // Mark response as processed and signal waiting thread
  pthread_mutex_lock(&args->response_mutex);
  args->response_processed = 1;
  pthread_cond_signal(&args->response_cond);
  pthread_mutex_unlock(&args->response_mutex);
}

static void oake_connection_callback(tcp_client_t *client, int connected,
                                     void *user_data) {
  oake_thread_args_t *args = (oake_thread_args_t *)user_data;

  if (connected) {
    printf("OAKE Logic: TCP connection established successfully\n");
    // Reset response processed flag for new connection
    pthread_mutex_lock(&args->response_mutex);
    args->response_processed = 0;
    pthread_mutex_unlock(&args->response_mutex);

    // Execute OAKE business logic only after successful connection
    execute_oake_business_logic(client, args);
  } else {
    printf("OAKE Logic: TCP connection lost or failed, auto-reconnect will "
           "retry\n");
    // Don't signal failure immediately since auto-reconnect will handle it
    // Only log the connection loss event
  }
}

static void set_oake_ip_addresses(oake_generic_data_t *oake_data,
                                  const char *host_ip, const char *peer_ip) {
  printf("OAKE Logic: Setting IP addresses in OAKE data...\n");
  strncpy(oake_data->src_ip, host_ip, OAKE_MAX_IP_LEN - 1);
  oake_data->src_ip[OAKE_MAX_IP_LEN - 1] = '\0'; // Ensure null termination
  strncpy(oake_data->dst_ip, peer_ip, OAKE_MAX_IP_LEN - 1);
  oake_data->dst_ip[OAKE_MAX_IP_LEN - 1] = '\0'; // Ensure null termination
  printf("OAKE Logic: Set src_ip=%s, dst_ip=%s\n", oake_data->src_ip,
         oake_data->dst_ip);
}

static void execute_oake_business_logic(tcp_client_t *client,
                                        oake_thread_args_t *args) {
  printf("OAKE Logic: Starting OAKE business logic execution\n");

  unsigned char device_id_buf[OAKE_MAX_ID_LEN];
  uint32_t device_id_len = OAKE_MAX_ID_LEN;

  if (init_device_id(device_id_buf, &device_id_len) != 0) {
    printf("OAKE Logic: Failed to initialize device ID\n");
    return;
  }

  if (init_oake_session(device_id_buf, device_id_len, &args->oake_data) != 0) {
    printf("OAKE Logic: Failed to initialize OAKE session\n");
    return;
  }

  printf("OAKE Logic: OAKE initialization completed successfully\n");

  printf("OAKE Logic: Creating OAKE request...\n");
  int result = oake_create_request(&args->oake_data);
  if (result != 0) {
    fprintf(stderr,
            "OAKE Logic: Failed to create OAKE request, error code: %d\n",
            result);
    return;
  }
  printf("OAKE Logic: OAKE request created successfully\n");

  set_oake_ip_addresses(&args->oake_data, args->host_ip, args->peer_ip);

  printf("OAKE Logic: Serializing OAKE data...\n");
  size_t serialized_size = oake_get_serialized_size(&args->oake_data);
  if (serialized_size == 0) {
    fprintf(stderr, "OAKE Logic: Failed to get serialized size\n");
    return;
  }

  uint8_t *serialized_buffer = malloc(serialized_size);
  if (!serialized_buffer) {
    fprintf(stderr,
            "OAKE Logic: Failed to allocate memory for serialized buffer\n");
    return;
  }

  int serialized_bytes =
      oake_serialize(&args->oake_data, serialized_buffer, serialized_size);
  if (serialized_bytes < 0) {
    fprintf(stderr, "OAKE Logic: Failed to serialize OAKE data\n");
    free(serialized_buffer);
    return;
  }
  printf("OAKE Logic: OAKE data serialized successfully, size: %d bytes\n",
         serialized_bytes);

  printf("OAKE Logic: Sending authentication message...\n");
  if (send_auth_message(client, serialized_buffer, serialized_bytes) != 0) {
    fprintf(stderr, "OAKE Logic: Failed to send authentication message\n");
    free(serialized_buffer);
    return;
  }

  free(serialized_buffer);
  printf("OAKE Logic: OAKE authentication process initiated successfully\n");
}

static void *oake_thread_main(void *arg) {
  printf("OAKE Logic: OAKE thread started\n");

  oake_thread_args_t *args = (oake_thread_args_t *)arg;
  if (!args) {
    fprintf(stderr, "OAKE Logic: Thread arguments are NULL\n");
    return NULL;
  }

  // Reset response processed flag for this session with mutex protection
  pthread_mutex_lock(&args->response_mutex);
  args->response_processed = 0;
  pthread_mutex_unlock(&args->response_mutex);

  printf("OAKE Logic: Initializing TCP client to connect to %s:%d\n",
         args->peer_ip, args->peer_port);
  args->oake_client = tcp_client_create(args->peer_ip, args->peer_port, NULL);
  if (!args->oake_client) {
    fprintf(stderr, "OAKE Logic: Failed to create TCP client\n");
    goto cleanup;
  }

  printf("OAKE Logic: Setting connection callback\n");
  tcp_client_set_connection_callback(args->oake_client,
                                     oake_connection_callback, args);

  printf("OAKE Logic: Setting response callback for handling peer responses\n");
  tcp_client_set_response_callback(args->oake_client, oake_response_callback,
                                   args);

  printf("OAKE Logic: Enabling auto-reconnect with 10 second interval\n");
  tcp_client_set_auto_reconnect(args->oake_client, 1, 10);

  printf("OAKE Logic: Initiating connection to peer %s:%d\n", args->peer_ip,
         args->peer_port);
  if (tcp_client_connect(args->oake_client) != 0) {
    fprintf(stderr, "OAKE Logic: Failed to initiate connection to peer %s:%d\n",
            args->peer_ip, args->peer_port);
    goto cleanup;
  }

  printf("OAKE Logic: Connection initiated, waiting for connection events\n");

  // Keep the thread alive and wait for OAKE handshake completion
  // Use condition variable for elegant waiting, allowing for multiple
  // reconnection attempts
  pthread_mutex_lock(&args->response_mutex);
  while (args->oake_client && args->response_processed == 0) {
    // Wait for response processing completion or timeout (120 seconds)
    // Longer timeout to allow for multiple reconnection attempts
    struct timespec timeout;
    clock_gettime(CLOCK_REALTIME, &timeout);
    timeout.tv_sec +=
        120; // 120 second timeout to allow for multiple reconnection attempts

    int wait_result = pthread_cond_timedwait(&args->response_cond,
                                             &args->response_mutex, &timeout);
    if (wait_result == ETIMEDOUT) {
      printf("OAKE Logic: Timeout waiting for OAKE response (120 seconds)\n");
      printf("OAKE Logic: Auto-reconnect may still be active, but giving up "
             "waiting\n");
      break;
    } else if (wait_result != 0) {
      printf("OAKE Logic: Error waiting for OAKE response: %d\n", wait_result);
      break;
    }

    // Check if we got signaled due to successful completion
    if (args->response_processed == 1) {
      printf("OAKE Logic: OAKE handshake completed successfully\n");
      break;
    }
  }
  pthread_mutex_unlock(&args->response_mutex);

  // Check if response was processed successfully
  if (args->response_processed == 1) {
    printf("OAKE Logic: OAKE handshake completed successfully\n");
  } else {
    printf(
        "OAKE Logic: OAKE handshake did not complete within timeout period\n");
    printf("OAKE Logic: TCP client with auto-reconnect will continue running "
           "in background\n");
  }

  // Handle TCP client cleanup based on handshake result
  if (args->response_processed == 1) {
    // Successful handshake - clean up client after completion
    if (args->oake_client) {
      printf("OAKE Logic: Cleaning up TCP client after OAKE completion\n");
      tcp_client_destroy(args->oake_client);
      args->oake_client = NULL;
    }
  } else {
    // Handshake not completed within timeout - clean up
    if (args->oake_client) {
      printf("OAKE Logic: Cleaning up TCP client due to timeout\n");
      tcp_client_destroy(args->oake_client);
      args->oake_client = NULL;
    }
  }

cleanup:
  // Clean up TCP client in error cases (early exit scenarios)
  if (args->oake_client && args->response_processed != 1) {
    printf("OAKE Logic: Cleaning up TCP client due to error or early exit\n");
    tcp_client_destroy(args->oake_client);
    args->oake_client = NULL;
  }

  if (args) {
    free(args);
  }
  return NULL;
}

static int load_ip_mapping_config(void) {
  printf("OAKE Logic: Loading IP mapping configuration...\n");
  if (init_ip_map("ip_mapping.txt") != 0) {
    fprintf(stderr, "OAKE Logic: Failed to initialize IP mapping from file\n");
    return -1;
  }
  return 0;
}

static int get_source_ip_list(char ***src_ips, unsigned int *ip_count) {
  printf("OAKE Logic: Getting all source IP addresses...\n");
  if (get_all_src_ips(src_ips, ip_count) != 0) {
    fprintf(stderr, "OAKE Logic: Failed to get source IP list\n");
    return -1;
  }

  if (*ip_count == 0) {
    printf(
        "OAKE Logic: No IP mappings found, no OAKE threads will be started\n");
    return 0;
  }

  printf("OAKE Logic: Found %u source IP addresses\n", *ip_count);
  return 0;
}

static oake_thread_args_t *create_oake_thread_args(const task_config_t *config,
                                                   const char *peer_ip) {
  oake_thread_args_t *args = malloc(sizeof(oake_thread_args_t));
  if (!args) {
    fprintf(stderr,
            "OAKE Logic: Failed to allocate memory for OAKE thread arguments "
            "(IP: %s)\n",
            peer_ip);
    return NULL;
  }

  args->is_master = config->csl.is_master;
  strncpy(args->peer_ip, peer_ip, MAX_IP_ADDR_LEN - 1);
  args->peer_ip[MAX_IP_ADDR_LEN - 1] = '\0';
  strncpy(args->host_ip, config->auth_server.listen_ip, MAX_IP_ADDR_LEN - 1);
  args->host_ip[MAX_IP_ADDR_LEN - 1] = '\0';
  args->peer_port = config->auth_server.port;

  args->oake_client = NULL;
  args->response_processed = 0;
  pthread_cond_init(&args->response_cond, NULL);
  pthread_mutex_init(&args->response_mutex, NULL);

  return args;
}

static void cleanup_oake_thread_args(oake_thread_args_t *args) {
  if (args) {
    pthread_cond_destroy(&args->response_cond);
    pthread_mutex_destroy(&args->response_mutex);
    free(args);
  }
}

static int create_single_oake_thread(const task_config_t *config,
                                     const char *peer_ip) {
  printf("OAKE Logic: Creating OAKE thread for peer IP: %s\n", peer_ip);

  oake_thread_args_t *args = create_oake_thread_args(config, peer_ip);
  if (!args) {
    return -1;
  }

  pthread_t thread_id;
  if (pthread_create(&thread_id, NULL, oake_thread_main, args) != 0) {
    fprintf(stderr, "OAKE Logic: Failed to create OAKE thread for IP: %s\n",
            peer_ip);
    cleanup_oake_thread_args(args);
    return -1;
  }

  if (pthread_detach(thread_id) != 0) {
    fprintf(stderr, "OAKE Logic: Failed to detach OAKE thread for IP: %s\n",
            peer_ip);
    // Not necessarily fatal, but good to log
  }

  printf("OAKE Logic: OAKE thread created successfully for peer IP: %s\n",
         peer_ip);
  return 0;
}

static int create_oake_threads_for_ips(const task_config_t *config,
                                       char **src_ips, unsigned int ip_count) {
  int threads_created = 0;

  printf("OAKE Logic: Starting OAKE threads for %u IP addresses...\n",
         ip_count);

  for (unsigned int i = 0; i < ip_count; i++) {
    if (create_single_oake_thread(config, src_ips[i]) == 0) {
      threads_created++;
    }
    // Continue with next IP even if current one fails
  }

  return threads_created;
}

int start_oake_thread(const task_config_t *config) {
  if (!config) {
    fprintf(stderr, "OAKE Logic: Cannot start OAKE thread, config is NULL.\n");
    return -1;
  }

  // Check if this node is configured as master
  if (config->csl.is_master == 0) {
    printf("OAKE Logic: This node is not configured as master (is_master=0), "
           "OAKE thread will not be started.\n");
    return 0; // Not an error, just not starting the thread
  }

  printf("OAKE Logic: Initializing OAKE threads (is_master=1)...\n");

  if (load_ip_mapping_config() != 0) {
    return -1;
  }

  char **src_ips = NULL;
  unsigned int ip_count = 0;

  if (get_source_ip_list(&src_ips, &ip_count) != 0) {
    cleanup_ip_map();
    return -1;
  }

  if (ip_count == 0) {
    free_ip_array(src_ips, ip_count);
    cleanup_ip_map();
    return 0;
  }

  int threads_created = create_oake_threads_for_ips(config, src_ips, ip_count);

  free_ip_array(src_ips, ip_count);
  cleanup_ip_map();

  if (threads_created > 0) {
    printf("OAKE Logic: Successfully created %d OAKE threads out of %u IP "
           "addresses\n",
           threads_created, ip_count);
    return 0;
  } else {
    fprintf(stderr, "OAKE Logic: Failed to create any OAKE threads\n");
    return -1;
  }
}
