#ifndef RAW_SENDER_H
#define RAW_SENDER_H

/**
 * @brief Initializes the raw sender module, including the raw socket and
 * libevent base if not already initialized. This function is thread-safe
 * and idempotent.
 *
 * @return 0 on success or if already initialized, -1 on failure.
 */
int initialize_sender(void);

/**
 * @brief Gets the file descriptor of the raw socket.
 * IMPORTANT: initialize_sender() must be successfully called before using this.
 *
 * @return The raw socket file descriptor, or -1 if not initialized or on error.
 */
int get_raw_socket_fd(void);

/**
 * @brief Cleans up resources used by the raw sender, including closing the
 * raw socket and freeing the libevent base. This function is thread-safe.
 */
void cleanup_raw_sender(void);

#endif // RAW_SENDER_H
