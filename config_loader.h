#ifndef CONFIG_LOADER_H
#define CONFIG_LOADER_H

#include "include/bsk_api.h"
#include <stddef.h>
#include <stdint.h>

#define DEFAULT_ETH_DEV "eth0"
#define DEFAULT_PKT_TRIM_BYTES 32
#define DEFAULT_MTU 1500
#define DEFAULT_PROTOCOL 0x0001
#define DEFAULT_LOCAL_ID 1
#define DEFAULT_REMOTE_ID 2
#define DEFAULT_LOCAL_PORT 12345
#define DEFAULT_REMOTE_PORT 54321
#define DEFAULT_SITP_Q_CAP 1024
#define DEFAULT_SEND_DELAY 0 // Delay in microseconds (0 = no delay)
#define DEFAULT_QUEUE_NUM 0
#define MAX_ETH_DEV_LEN 32
#define MAX_IP_ADDR_LEN 40

#define DEFAULT_DEVICE_NO 0
#define DEFAULT_PIN "00000000"
#define MAX_PIN_LEN 64

#define DEFAULT_ENC_CERT_NAME "CAROOTCERTE_DEVENCCERT_ECC359"
#define ENC_CERT_NAME_LEN 64
#define DEFAULT_SIGN_CERT_NAME "CAROOTCERTE_DEVSIGNCERT_ECC359"
#define SIGN_CERT_NAME_LEN 64
#define DEFAULT_IV "00112233445566778899AABBCCDDEEFF" // Default IV (16 bytes)
#define IV_LEN 33 // Max length for IV string (32 hex chars + null terminator)
#define DEFAULT_ENCRYPT 0 // 1 = enabled, 0 = disabled
// Default values for auth_server settings, based on values in sm_algo.h.
#define DEFAULT_SYMM_ALGO 0x740000  // PFSJ1006B
#define DEFAULT_ASYMM_ALGO 0x170000 // P1GWY0613_IEA
#define DEFAULT_ENC_MODE 0x0004     // CS_SYMMALGMODE_LAPE

#define DEFAULT_AUTH_SERVER_PORT 7015
#define DEFAULT_AUTH_SERVER_IP "0.0.0.0" // Listen on all interfaces

#define DEFAULT_LOG_TO_FILE 0
#define DEFAULT_LOG_FILENAME "nfq_server.log"
#define MAX_LOG_FILENAME_LEN 256
#define DEFAULT_LOG_LEVEL LOG_TRACE
#define DEFAULT_IS_MASTER 0 // 0 = not master

typedef struct {
  char eth_dev[MAX_ETH_DEV_LEN];
  int mtu;
  uint16_t protocol;
  uint16_t local_id;
  uint16_t remote_id;
  uint16_t local_port;
  uint16_t remote_port;
  unsigned int pkt_trim_bytes;
  int sitp_q_cap;
  unsigned int send_delay; // Delay after sending a packet, in microseconds
  uint16_t queue_num;      // Queue number for nfq_create_queue
} sitp_task_config_t;

typedef struct {
  int device_no;
  char pin[MAX_PIN_LEN];
  char enc_cert_name[ENC_CERT_NAME_LEN];
  char sign_cert_name[SIGN_CERT_NAME_LEN];
  char iv[IV_LEN];
  int encrypt; // 1 = enabled, 0 = disabled
  unsigned int symm_algo;
  unsigned int asymm_algo;
  unsigned int enc_mode;
  int is_master; // 0 = no, 1 = yes
} csl_config_t;

typedef struct {
  int port;
  char listen_ip[MAX_IP_ADDR_LEN];
} auth_server_config_t;

typedef struct {
  int log_to_file;
  int log_level;
  char log_filename[MAX_LOG_FILENAME_LEN];
} logging_config_t;

typedef struct {
  sitp_task_config_t sitp_task;
  csl_config_t csl;
  auth_server_config_t auth_server;
  logging_config_t logging;
} task_config_t;

/*
 * Loads configuration from the specified INI file.
 * If the file cannot be read or some parameters are missing,
 * default values will be used for those parameters.
 *
 * @param filename The path to the INI configuration file.
 * @param config Pointer to the task_config_t struct to be filled.
 * @return 0 on success, -1 on file open error (defaults will still be set).
 */
int load_task_config(const char *filename, task_config_t *config);

/**
 * @brief Initializes the global configuration by loading it from the specified
 * file.
 *
 * This function should be called once at the application startup.
 * It populates a global instance of task_config_t.
 *
 * @param filename The path to the INI configuration file.
 */
void initialize_global_config(const char *filename);

/**
 * @brief Retrieves a pointer to the globally loaded task configuration.
 *
 * @return const task_config_t* Pointer to the global configuration struct.
 *         Returns a pointer to a struct with defaults if initialization failed
 * or wasn't called, but check logs from initialize_global_config for issues.
 */
const task_config_t *get_global_config(void);

#endif /* CONFIG_LOADER_H */
