#define _GNU_SOURCE // For certain networking features and definitions
#include "background_task.h"
#include "config_loader.h"
#include "include/sitp_lib.h"
#include "packet_handler.h" // For raw_packet_receive_callback
#include <pthread.h>
#include <stdio.h>
#include <unistd.h>

void *start_task(void *arg) {
  (void)arg;
  printf("Background task starting SITP_LIB...\n");

  const task_config_t *config = get_global_config();

  sitp_lib_start(config->sitp_task.eth_dev, config->sitp_task.mtu,
                 config->sitp_task.protocol, config->sitp_task.local_id,
                 config->sitp_task.remote_id, config->sitp_task.local_port,
                 config->sitp_task.remote_port, raw_packet_receive_callback,
                 NULL);

  // sitp_lib_start is a blocking call.
  printf("SITP_LIB has stopped.\n");
  return NULL;
}

void init_background_task(void) {
  pthread_t thread_id;
  printf("Initializing background task module...\n");

  int err = pthread_create(&thread_id, NULL, &start_task, NULL);
  if (err != 0) {
    perror("Failed to create background thread");
  } else {
    printf("Background thread created successfully.\n");
    pthread_detach(thread_id);
  }
}
