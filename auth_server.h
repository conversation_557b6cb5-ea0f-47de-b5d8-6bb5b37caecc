#ifndef AUTH_SERVER_H
#define AUTH_SERVER_H

#include "config_loader.h" // For task_config_t

/**
 * @brief Initializes and starts the authentication server in a new thread.
 *
 * The server will listen on the port specified in the configuration.
 *
 * @param config Pointer to the application configuration.
 * @return 0 on success, -1 on failure to start the server thread.
 */
int start_auth_server(const task_config_t *config);

#endif /* AUTH_SERVER_H */
