#ifndef CONFIG_HANDLER_H
#define CONFIG_HANDLER_H

#include "message_common.h"
#include <event2/bufferevent.h>
#include <stdint.h>

/**
 * Validate config message payload size
 * @param host_len Payload length in host byte order
 * @return 0 on success, -1 on failure
 */
int validate_config_payload(uint32_t host_len);

/**
 * Process config message
 * @param bev Buffer event for the connection
 * @param header Message header
 * @param payload Message payload (can be NULL if host_len is 0)
 * @param host_len Payload length in host byte order
 * @return 0 on success, -1 on failure (connection should be closed)
 */
int process_config_message(struct bufferevent *bev,
                           const message_header_t *header,
                           const unsigned char *payload, uint32_t host_len);

#endif /* CONFIG_HANDLER_H */