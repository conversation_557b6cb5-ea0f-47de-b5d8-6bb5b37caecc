#include "auth_handler.h"
#include "log.h"
#include "message_common.h"
#include "oake.h"
#include <arpa/inet.h>
#include <event2/bufferevent.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Maximum authentication payload size
#define MAX_AUTH_PAYLOAD_SIZE 4096

int validate_auth_payload(uint32_t host_len) {
  if (host_len == 0) {
    log_error("Authentication message must have payload for OAKE "
              "processing. Closing connection");
    return -1;
  }

  if (host_len > MAX_AUTH_PAYLOAD_SIZE) {
    log_error("Authentication message payload length %u exceeds maximum allowed "
              "size %d. Closing connection",
              host_len, MAX_AUTH_PAYLOAD_SIZE);
    return -1;
  }

  return 0;
}

int process_auth_request(struct bufferevent *bev,
                         const message_header_t *header, unsigned char *payload,
                         uint32_t host_len, oake_generic_data_t *oake_data,
                         const char *src_ip) {
  log_info("Processing authentication request message: payload_len=%u, src_ip=%s",
           host_len, src_ip);

  if (payload == NULL || host_len == 0) {
    log_error("process_auth_request: Invalid payload data");
    return -1;
  }

  // Step 1: Parse device_id, rnd, cert from received data using
  // oake_deserialize
  oake_generic_data_t temp_data;
  memset(&temp_data, 0, sizeof(oake_generic_data_t));

  int deserialize_result = oake_deserialize(payload, host_len, &temp_data);
  if (deserialize_result != 0) {
    log_error("process_auth_request: Failed to deserialize OAKE data, error: %d",
              deserialize_result);
    return -1;
  }

  log_debug("process_auth_request: Successfully parsed device_id_len=%u, "
            "cert_len=%u, rnd_len=%u",
            temp_data.device_id_len, temp_data.cert_len, temp_data.rnd_len);

  // Step 2: Use parsed device_id to call oake_init, handling oake_data
  oake_generic_data_t *data_out = oake_data;
  int init_result =
      oake_init(temp_data.device_id, temp_data.device_id_len, &data_out);
  if (init_result != 0) {
    log_error("process_auth_request: Failed to initialize OAKE, error: %d",
              init_result);
    return -1;
  }

  log_debug("process_auth_request: OAKE initialized successfully");

  // Step 3: After init, put parsed rnd and cert into oake_data
  if (temp_data.rnd_len > 0) {
    memcpy(oake_data->rnd, temp_data.rnd, temp_data.rnd_len);
    oake_data->rnd_len = temp_data.rnd_len;
  }

  if (temp_data.cert_len > 0) {
    memcpy(oake_data->cert, temp_data.cert, temp_data.cert_len);
    oake_data->cert_len = temp_data.cert_len;
  }

  // Set the src_ip in the oake_data
  strncpy(oake_data->src_ip, src_ip, OAKE_MAX_IP_LEN - 1);
  oake_data->src_ip[OAKE_MAX_IP_LEN - 1] = '\0';

  log_debug("process_auth_request: Set rnd_len=%u, cert_len=%u into responder data",
            oake_data->rnd_len, oake_data->cert_len);

  // Step 4: Call oake_create_response
  int response_result = oake_create_response(oake_data);
  if (response_result != 0) {
    log_error("process_auth_request: Failed to create OAKE response, error: %d",
              response_result);
    return -1;
  }

  log_debug("process_auth_request: OAKE response created successfully");

  // Step 5: Call oake_serialize to serialize data
  size_t serialized_size = oake_get_serialized_size(oake_data);
  if (serialized_size == 0) {
    log_error("process_auth_request: Failed to get serialized size");
    return -1;
  }

  uint8_t *serialized_buffer = malloc(serialized_size);
  if (!serialized_buffer) {
    log_error("process_auth_request: Failed to allocate memory for "
              "serialized buffer");
    return -1;
  }

  int serialized_bytes =
      oake_serialize(oake_data, serialized_buffer, serialized_size);
  if (serialized_bytes < 0) {
    log_error("process_auth_request: Failed to serialize OAKE data");
    free(serialized_buffer);
    return -1;
  }

  log_debug("process_auth_request: OAKE data serialized successfully, size: %d "
            "bytes",
            serialized_bytes);

  // Step 6: Assemble type 2 message according to message_common.h and write
  // back response
  message_header_t response_header;
  memcpy(response_header.magic, MAGIC_NUMBER, MAGIC_LEN);
  response_header.len = serialized_bytes;
  response_header.type = MSG_TYPE_AUTHENTICATION;

  // Write header first
  if (bufferevent_write(bev, &response_header, sizeof(message_header_t)) != 0) {
    log_error("process_auth_request: Failed to write response header");
    free(serialized_buffer);
    return -1;
  }

  // Write payload
  if (bufferevent_write(bev, serialized_buffer, serialized_bytes) != 0) {
    log_error("process_auth_request: Failed to write response payload");
    free(serialized_buffer);
    return -1;
  }

  log_info("process_auth_request: Authentication response sent successfully, "
           "total size: %zu bytes",
           sizeof(message_header_t) + serialized_bytes);

  free(serialized_buffer);

  // Save the updated oake_data to the hash table by IP
  if (oake_set_data_by_ip(src_ip, oake_data) != 0) {
    log_error("process_auth_request: Failed to save OAKE data for IP %s",
              src_ip);
    return -1;
  }

  log_debug("process_auth_request: OAKE data saved for IP %s", src_ip);

  return 0;
}

int process_auth_acknowledgment(struct bufferevent *bev,
                                const message_header_t *header,
                                unsigned char *payload, uint32_t host_len,
                                oake_generic_data_t *oake_data,
                                const char *src_ip) {
  log_info("Processing authentication acknowledgment message: payload_len=%u, "
           "src_ip=%s",
           host_len, src_ip);

  if (payload == NULL || host_len == 0) {
    log_error("process_auth_acknowledgment: Invalid payload data");
    return -1;
  }

  // Check if we have an initialized OAKE session (ctx should not be NULL)
  if (oake_data->ctx == NULL) {
    log_error("process_auth_acknowledgment: OAKE session not initialized for IP %s, "
              "cannot process acknowledgment",
              src_ip);
    return -1;
  }

  // Step 1: Deserialize acknowledgment data into oake_data
  log_debug("process_auth_acknowledgment: Deserializing OAKE acknowledgment data");
  int deserialize_result = oake_deserialize(payload, host_len, oake_data);
  if (deserialize_result != 0) {
    log_error("process_auth_acknowledgment: Failed to deserialize OAKE "
              "acknowledgment data, error: %d",
              deserialize_result);
    return -1;
  }

  log_debug("process_auth_acknowledgment: OAKE acknowledgment data deserialized "
            "successfully");

  // Step 2: Process acknowledgment using oake_process_acknowledgement
  log_debug("process_auth_acknowledgment: Processing OAKE acknowledgment");
  int process_result = oake_process_acknowledgement(oake_data);
  if (process_result != 0) {
    log_error("process_auth_acknowledgment: Failed to process OAKE "
              "acknowledgment, error: %d",
              process_result);
    return -1;
  }

  log_debug("process_auth_acknowledgment: OAKE acknowledgment processed "
            "successfully");

  // Step 3: Finalize OAKE session
  log_debug("process_auth_acknowledgment: Finalizing OAKE session");
  int finalize_result = oake_finalize_session(oake_data);
  if (finalize_result != 0) {
    log_error("process_auth_acknowledgment: Failed to finalize OAKE session, "
              "error: %d",
              finalize_result);
    return -1;
  }

  log_info("process_auth_acknowledgment: OAKE session finalized successfully");
  log_info("process_auth_acknowledgment: OAKE authentication handshake completed "
           "on responder side for IP %s",
           src_ip);

  // Update the oake_data in the hash table
  if (oake_set_data_by_ip(src_ip, oake_data) != 0) {
    log_error("process_auth_acknowledgment: Failed to update OAKE data for IP %s",
              src_ip);
    return -1;
  }

  // Note: No response is sent for acknowledgment processing as it's the final
  // step
  return 0;
}

int process_auth_message(struct bufferevent *bev,
                         const message_header_t *header, unsigned char *payload,
                         uint32_t host_len) {
  log_info("Processing authentication message: payload_len=%u", host_len);

  if (payload == NULL || host_len == 0) {
    log_error("process_auth_message: Invalid payload data");
    return -1;
  }

  // Step 1: First deserialize the data to get src_ip
  oake_generic_data_t temp_data;
  memset(&temp_data, 0, sizeof(oake_generic_data_t));

  int deserialize_result = oake_deserialize(payload, host_len, &temp_data);
  if (deserialize_result != 0) {
    log_error("process_auth_message: Failed to deserialize data to get src_ip, "
              "error: %d",
              deserialize_result);
    return -1;
  }

  const char *src_ip = temp_data.src_ip;
  if (strlen(src_ip) == 0) {
    log_error("process_auth_message: src_ip is empty in received data");
    return -1;
  }

  log_debug("process_auth_message: Extracted src_ip=%s from payload", src_ip);

  // Step 2: Get OAKE data by IP address
  oake_generic_data_t *oake_data = oake_get_data_by_ip(src_ip);

  if (oake_data == NULL) {
    // No existing data found, this is a new authentication request
    log_debug("process_auth_message: No existing OAKE data for IP %s, treating as "
              "new request",
              src_ip);

    // Create a new oake_data structure and allocate memory for it
    oake_generic_data_t *new_oake_data = malloc(sizeof(oake_generic_data_t));
    if (!new_oake_data) {
      log_error("process_auth_message: Failed to allocate memory for new "
                "OAKE data");
      return -1;
    }
    memset(new_oake_data, 0, sizeof(oake_generic_data_t));

    int result = process_auth_request(bev, header, payload, host_len,
                                      new_oake_data, src_ip);
    free(new_oake_data); // Free after use since oake_set_data_by_ip should copy
                         // the data
    return result;
  } else {
    int release_rv = oake_release_resources(oake_data);
    if (release_rv != 0) {
      log_warn("oake_set_data_by_ip: Warning - failed to release existing "
               "resources for IP %s, error: %d",
               src_ip, release_rv);
    }

    // Existing data found, check if session is initialized
    if (oake_data->ctx == NULL) {
      // Session not initialized, treat as request
      log_debug("process_auth_message: OAKE data exists for IP %s but session not "
                "initialized, treating as request",
                src_ip);
      return process_auth_request(bev, header, payload, host_len, oake_data,
                                  src_ip);
    } else {
      // Session initialized, treat as acknowledgment
      log_debug("process_auth_message: OAKE session initialized for IP %s, "
                "treating as acknowledgment",
                src_ip);
      return process_auth_acknowledgment(bev, header, payload, host_len,
                                         oake_data, src_ip);
    }
  }
}
