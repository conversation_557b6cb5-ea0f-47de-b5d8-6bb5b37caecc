#ifndef DEVICE_H
#define DEVICE_H

#include "include/bsk_api.h" // For CPCI_*, CS_*, CSR_*

// Forward declaration of the handle structure (now internal to
// device_manager.c) typedef struct device_handle_s device_handle_t; // This is
// no longer needed externally

/**
 * @brief Initializes the device.
 * Opens the device and logs in using parameters from the global configuration.
 * This function manages an internal, static device handle.
 *
 * @return CSR_OK on success, or an error code from bsk_api.h on failure.
 *         CSR_SYSTEM_ERR if already initialized.
 */
int device_init(void);

/**
 * @brief Cleans up the device.
 * Logs out of the device, closes it, and frees the internal handle.
 *
 * @return CSR_OK on success, or an error code from bsk_api.h on failure.
 */
int device_cleanup(void);

/**
 * @brief Loads global certificates (e.g., encryption and signing certificates)
 * from the device.
 *
 * The names of the certificates to load are fetched from the global
 * configuration. The loaded data is stored in static global variables within
 * device_manager.c. This function should be called after device_init and
 * successful login. It uses the internal device handle.
 *
 * @return CSR_OK on success, or an error code from bsk_api.h on failure.
 *         CSR_ERR_DEVICE_HANDLE if the device is not initialized.
 */
int device_load_global_certs(void);

/**
 * @brief Retrieves the globally loaded encryption certificate data.
 *
 * @param[out] data Pointer to a const unsigned char* that will be set to the
 *                  address of the encryption certificate data.
 * @param[out] len Pointer to an int that will be set to the length of the data.
 * @return CSR_OK if data is available, CSR_SYSTEM_ERR if not loaded or empty,
 *         CSR_INVALIDPARAMETER for NULL arguments.
 */
int get_enc_cert_data(const unsigned char **data, int *len);

/**
 * @brief Retrieves the globally loaded signing certificate data.
 *
 * @param[out] data Pointer to a const unsigned char* that will be set to the
 *                  address of the signing certificate data.
 * @param[out] len Pointer to an int that will be set to the length of the data.
 * @return CSR_OK if data is available, CSR_SYSTEM_ERR if not loaded or empty,
 *         CSR_INVALIDPARAMETER for NULL arguments.
 */
int get_sign_cert_data(const unsigned char **data, int *len);

/**
 * @brief Cleans up (frees) the globally loaded certificate data.
 *
 * This should be called during application shutdown, after the device is no
 * longer needed.
 */
void device_cleanup_global_certs(void);

/**
 * @brief Retrieves the raw device API handle.
 * This function provides external access to the `device_api_handle` stored
 * internally. Use with caution, as direct manipulation of the handle can lead
 * to instability.
 *
 * @return A void pointer to the device API handle if initialized, otherwise
 * NULL.
 */
void *device_get_api_handle(void);

/**
 * @brief Retrieves the device ID.
 * This function calls CPCI_GetDeviceID to fetch the unique identifier of the
 * device.
 *
 * @param[out] device_id_buf Buffer to store the retrieved device ID.
 * @param[in,out] device_id_len Pointer to an integer. On input, it should
 * contain the size of the device_id_buf. On successful output, it will contain
 * the actual length of the device ID written to the buffer.
 * @return CSR_OK on success.
 *         CSR_ERR_DEVICE_HANDLE if the device is not initialized.
 *         CSR_INVALIDPARAMETER if device_id_buf or device_id_len is NULL.
 *         CSR_BADLENGTH if *device_id_len (buffer size) is not positive.
 *         Other error codes from bsk_api.h on failure from CPCI_GetDeviceID.
 */
int device_get_id(unsigned char *device_id_buf, int *device_id_len);
/**
 * @brief Parses a certificate and extracts specified field data.
 *
 * This function encapsulates the CPCI_CertParse API call, using the internal
 * device handle. It can extract various certificate fields such as subject
 * name, public key, serial number, etc.
 *
 * @param[in] cert_data Pointer to the certificate data to parse.
 * @param[in] cert_len Length of the certificate data in bytes.
 * @param[in] field_type The certificate field type to extract (use CERT_FIELD_*
 * constants from bsk_api.h).
 * @param[out] output_data Buffer to store the extracted field data.
 * @param[in,out] output_len Pointer to the size of output_data buffer (input),
 *                           and actual length of extracted data (output).
 * @return CSR_OK on success, or an error code from bsk_api.h on failure.
 *         CSR_ERR_DEVICE_HANDLE if the device is not initialized.
 *         CSR_INVALIDPARAMETER for invalid input parameters.
 */
int device_cert_parse_public_key(const unsigned char *cert_data,
                                 unsigned int cert_len,
                                 unsigned char *output_data,
                                 unsigned int *output_len);

#endif // DEVICE_H
